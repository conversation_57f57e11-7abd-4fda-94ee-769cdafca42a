#!/usr/bin/env python3
"""
اختبار سريع للواجهة الرسومية
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_gui():
    """اختبار الواجهة الرسومية"""
    try:
        # اختبار استيراد المكتبات المطلوبة
        print("اختبار المكتبات المطلوبة...")
        
        import cv2
        print("✓ OpenCV")
        
        import numpy as np
        print("✓ NumPy")
        
        import PIL
        print("✓ Pillow")
        
        import tqdm
        print("✓ tqdm")
        
        import matplotlib
        print("✓ Matplotlib")
        
        # اختبار استيراد الوحدات المحلية
        print("\nاختبار الوحدات المحلية...")
        
        from config import CARTOON_STYLES
        print("✓ config")
        
        from utils import create_directories
        print("✓ utils")
        
        from image_cartoonizer import ImageCartoonizer
        print("✓ image_cartoonizer")
        
        from video_cartoonizer import VideoCartoonizer
        print("✓ video_cartoonizer")
        
        # اختبار الواجهة الرسومية
        print("\nاختبار الواجهة الرسومية...")
        
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # اختبار إنشاء نافذة
        test_window = tk.Toplevel(root)
        test_window.title("اختبار الواجهة")
        test_window.geometry("300x200")
        
        label = tk.Label(test_window, text="اختبار الواجهة الرسومية\nTest GUI", 
                        font=('Arial', 12), justify=tk.CENTER)
        label.pack(expand=True)
        
        def close_test():
            test_window.destroy()
            root.quit()
        
        button = tk.Button(test_window, text="إغلاق / Close", command=close_test)
        button.pack(pady=10)
        
        print("✓ tkinter")
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ All tests passed!")
        print("\nيمكنك الآن تشغيل الواجهة الرسومية:")
        print("You can now run the GUI:")
        print("python run_gui.py")
        
        # عرض النافذة لثانيتين ثم إغلاقها
        test_window.after(3000, close_test)
        root.mainloop()
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبة: {e}")
        print(f"❌ Import error: {e}")
        print("\nيرجى تثبيت المتطلبات:")
        print("Please install requirements:")
        print("pip install opencv-python numpy matplotlib Pillow tqdm")
        return False
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("اختبار برنامج محول الفيديو إلى كرتون")
    print("Video to Cartoon Converter Test")
    print("=" * 50)
    
    success = test_gui()
    
    if not success:
        input("\nاضغط Enter للخروج / Press Enter to exit...")
        sys.exit(1)
