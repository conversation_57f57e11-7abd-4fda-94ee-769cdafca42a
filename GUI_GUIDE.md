# دليل استخدام الواجهة الرسومية - محول الفيديو إلى كرتون

## تشغيل الواجهة الرسومية

### الطريقة الأولى: تشغيل مباشر
```bash
python run_gui.py
```

### الطريقة الثانية: تشغيل من ملف gui.py
```bash
python gui.py
```

## واجهة المستخدم

### 1. قسم اختيار الملف
- **الملف**: اختر ملف الفيديو أو الصورة المراد تحويلها
- **مجلد الإخراج**: حدد المجلد الذي ستُحفظ فيه النتائج

### 2. قسم إعدادات التحويل
- **نمط الكرتون**: اختر من بين ثلاثة أنماط:
  - **كلاسيكي**: متوازن ومناسب لمعظم الاستخدامات
  - **ناعم**: مناسب للمناظر الطبيعية والمشاهد الهادئة
  - **حاد**: مناسب للشخصيات والرسوم المتحركة

- **خيارات الفيديو** (تظهر فقط للفيديوهات):
  - **تحويل مقطع محدد**: لتحويل جزء معين من الفيديو
  - **البداية**: وقت بداية المقطع بالثواني
  - **المدة**: مدة المقطع بالثواني

### 3. قسم المعاينة
- **الأصلية**: عرض الصورة أو الإطار الأصلي
- **كرتون**: عرض النتيجة بعد التحويل
- **معاينة سريعة**: زر لمعاينة النتيجة قبل التحويل الكامل

### 4. قسم التحكم
- **بدء التحويل**: بدء عملية التحويل
- **إيقاف**: إيقاف العملية الجارية
- **فتح مجلد النتائج**: فتح مجلد الإخراج في مستكشف الملفات

### 5. قسم التقدم والسجل
- **شريط التقدم**: يعرض تقدم العملية
- **نص التقدم**: معلومات تفصيلية عن الحالة الحالية
- **سجل العمليات**: سجل مفصل بجميع العمليات والرسائل

## خطوات الاستخدام

### لتحويل صورة:
1. انقر على "تصفح" بجانب "الملف"
2. اختر صورة (JPG, PNG, BMP, إلخ)
3. اختر نمط الكرتون المناسب
4. انقر على "معاينة سريعة" لرؤية النتيجة
5. انقر على "بدء التحويل"
6. انتظر حتى اكتمال العملية

### لتحويل فيديو كامل:
1. انقر على "تصفح" بجانب "الملف"
2. اختر ملف فيديو (MP4, AVI, MOV, إلخ)
3. اختر نمط الكرتون المناسب
4. تأكد من عدم تفعيل "تحويل مقطع محدد"
5. انقر على "بدء التحويل"
6. راقب شريط التقدم حتى اكتمال العملية

### لتحويل مقطع من فيديو:
1. اختر ملف الفيديو
2. فعّل خيار "تحويل مقطع محدد"
3. حدد وقت البداية والمدة
4. انقر على "بدء التحويل"

## نصائح للاستخدام

### للحصول على أفضل النتائج:
- استخدم فيديوهات بدقة 1080p أو أقل
- تأكد من وجود إضاءة جيدة في الفيديو
- جرب الأنماط المختلفة لاختيار الأنسب

### لتوفير الوقت:
- استخدم "معاينة سريعة" قبل التحويل الكامل
- للفيديوهات الطويلة، جرب مقطع صغير أولاً
- استخدم النمط "كلاسيكي" للسرعة

### لتجنب المشاكل:
- تأكد من وجود مساحة كافية في مجلد الإخراج
- لا تغلق البرنامج أثناء المعالجة
- احفظ عملك قبل بدء معالجة فيديو طويل

## استكشاف الأخطاء

### مشكلة: "لا يمكن قراءة الملف"
- تأكد من أن الملف غير تالف
- جرب تحويل الملف إلى صيغة أخرى
- تأكد من أن الملف غير مفتوح في برنامج آخر

### مشكلة: بطء في المعالجة
- أغلق البرامج الأخرى لتوفير الذاكرة
- استخدم النمط "كلاسيكي"
- قلل من دقة الفيديو إذا أمكن

### مشكلة: توقف البرنامج
- تأكد من تثبيت جميع المتطلبات
- أعد تشغيل البرنامج
- تحقق من سجل العمليات للحصول على تفاصيل الخطأ

## الاختصارات

- **Ctrl+O**: فتح ملف (في بعض الأنظمة)
- **Escape**: إغلاق نوافذ الحوار
- **Enter**: تأكيد في نوافذ الحوار

## متطلبات النظام

- **نظام التشغيل**: Windows 10+, macOS 10.14+, Linux
- **الذاكرة**: 4 GB RAM (8 GB مُوصى به)
- **المعالج**: Intel i5 أو AMD Ryzen 5 (أو أفضل)
- **مساحة التخزين**: 2 GB مساحة فارغة
- **Python**: 3.7 أو أحدث

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من سجل العمليات في البرنامج
2. راجع ملف README.md للمزيد من التفاصيل
3. تأكد من تحديث جميع المكتبات المطلوبة

---

**ملاحظة**: هذا البرنامج مُحسّن للاستخدام الشخصي والتعليمي. للاستخدام التجاري، يُرجى مراجعة تراخيص المكتبات المستخدمة.
