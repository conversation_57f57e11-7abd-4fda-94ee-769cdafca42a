# دليل استكشاف الأخطاء - محول الفيديو إلى كرتون

## مشاكل شائعة وحلولها

### 🚫 مشاكل قراءة الملفات

#### المشكلة: "can't open/read file: check file path/integrity"
**الأسباب المحتملة:**
- مسار الملف يحتوي على رموز خاصة أو أحرف غير مدعومة
- اسم الملف يحتوي على رموز عربية أو رموز خاصة
- الملف تالف أو غير مكتمل
- صيغة الملف غير مدعومة

**الحلول:**
1. **إعادة تسمية الملف:**
   ```
   من: "صورة 2025-05-28 005704.png"
   إلى: "image_2025_05_28_005704.png"
   ```

2. **نسخ الملف إلى مجلد بسيط:**
   - انسخ الملف إلى مجلد مثل `C:\temp\` أو `D:\files\`
   - تأكد من أن المسار لا يحتوي على مسافات أو رموز خاصة

3. **تحويل صيغة الملف:**
   - استخدم برنامج تحرير الصور لحفظ الملف بصيغة أخرى
   - جرب صيغ مختلفة: JPG, PNG, BMP

4. **استخدام الواجهة الرسومية:**
   - الواجهة الرسومية تتعامل تلقائياً مع مشاكل المسارات
   - تستخدم طرق متعددة لقراءة الملفات

#### المشكلة: "الملف غير موجود"
**الحلول:**
- تأكد من وجود الملف في المسار المحدد
- تحقق من الأذونات (Permissions)
- تأكد من عدم حذف الملف أثناء المعالجة

### 🎬 مشاكل الفيديو

#### المشكلة: "لا يمكن فتح ملف الفيديو"
**الحلول:**
1. **تحقق من صيغة الفيديو:**
   - الصيغ المدعومة: MP4, AVI, MOV, MKV, WMV
   - حول الفيديو إلى MP4 إذا لزم الأمر

2. **تحقق من codec الفيديو:**
   ```bash
   # استخدم ffmpeg لتحويل الفيديو
   ffmpeg -i input.mkv -c:v libx264 -c:a aac output.mp4
   ```

3. **تقليل حجم الفيديو:**
   - قلل من دقة الفيديو إذا كان كبيراً جداً
   - استخدم خيار "تحويل مقطع محدد" للاختبار

#### المشكلة: بطء في معالجة الفيديو
**الحلول:**
- استخدم النمط "كلاسيكي" للسرعة
- قلل من دقة الفيديو
- أغلق البرامج الأخرى لتوفير الذاكرة
- استخدم مقاطع أقصر للاختبار

### 🖼️ مشاكل الصور

#### المشكلة: "لا يمكن تحميل الصورة"
**الحلول:**
1. **تحقق من صيغة الصورة:**
   - الصيغ المدعومة: JPG, JPEG, PNG, BMP, TIFF
   - تجنب الصيغ النادرة مثل WEBP أو HEIC

2. **تحقق من حجم الصورة:**
   - الصور الكبيرة جداً قد تسبب مشاكل
   - قلل من حجم الصورة إذا لزم الأمر

3. **تحويل الصورة:**
   ```python
   # استخدم PIL لتحويل الصورة
   from PIL import Image
   img = Image.open('input.webp')
   img.save('output.jpg', 'JPEG')
   ```

### 💾 مشاكل الحفظ

#### المشكلة: "فشل في حفظ الصورة/الفيديو"
**الحلول:**
1. **تحقق من مساحة القرص:**
   - تأكد من وجود مساحة كافية
   - نظف القرص الصلب إذا لزم الأمر

2. **تحقق من أذونات المجلد:**
   - تأكد من أن لديك صلاحية الكتابة في مجلد الإخراج
   - جرب مجلد مختلف مثل سطح المكتب

3. **تغيير مجلد الإخراج:**
   - استخدم مجلد بسيط مثل `C:\output\`
   - تجنب المجلدات المحمية بكلمة مرور

### 🧠 مشاكل الذاكرة

#### المشكلة: "نفاد الذاكرة" أو توقف البرنامج
**الحلول:**
1. **أغلق البرامج الأخرى:**
   - أغلق المتصفحات والبرامج الثقيلة
   - تأكد من توفر 4 GB ذاكرة على الأقل

2. **قلل من حجم الملفات:**
   - استخدم صور أصغر (أقل من 2000x2000)
   - استخدم فيديوهات أقصر (أقل من 5 دقائق)

3. **استخدم المعالجة التدريجية:**
   - استخدم خيار "تحويل مقطع محدد"
   - قسم الفيديو الطويل إلى مقاطع أصغر

### 🐍 مشاكل Python والمكتبات

#### المشكلة: "ModuleNotFoundError"
**الحلول:**
```bash
# إعادة تثبيت المتطلبات
pip uninstall opencv-python
pip install opencv-python

# أو تثبيت جميع المتطلبات مرة أخرى
pip install -r requirements.txt --force-reinstall
```

#### المشكلة: مشاكل في tkinter
**الحلول:**
- **Windows:** tkinter مثبت مع Python افتراضياً
- **Linux:** 
  ```bash
  sudo apt-get install python3-tk
  ```
- **macOS:** 
  ```bash
  brew install python-tk
  ```

### 🖥️ مشاكل الواجهة الرسومية

#### المشكلة: الواجهة لا تظهر أو تتجمد
**الحلول:**
1. **أعد تشغيل البرنامج:**
   ```bash
   python run_gui.py
   ```

2. **تحقق من دقة الشاشة:**
   - تأكد من أن دقة الشاشة مناسبة (1024x768 على الأقل)

3. **استخدم سطر الأوامر كبديل:**
   ```bash
   python main.py input.jpg --style classic
   ```

#### المشكلة: النص غير واضح أو مقطوع
**الحلول:**
- تأكد من دعم النظام للغة العربية
- جرب تغيير حجم النافذة
- استخدم دقة شاشة أعلى

### 🔧 نصائح عامة لتجنب المشاكل

#### 1. تحضير الملفات
- استخدم أسماء ملفات بسيطة (أحرف إنجليزية وأرقام فقط)
- ضع الملفات في مجلدات بسيطة
- تأكد من سلامة الملفات قبل المعالجة

#### 2. إعدادات النظام
- تأكد من تحديث Python إلى أحدث إصدار
- أعد تشغيل الحاسوب إذا واجهت مشاكل مستمرة
- تأكد من عدم وجود برامج مكافحة فيروسات تحجب البرنامج

#### 3. اختبار تدريجي
- ابدأ بصور صغيرة للاختبار
- جرب مقاطع فيديو قصيرة أولاً
- استخدم المعاينة السريعة قبل التحويل الكامل

#### 4. النسخ الاحتياطي
- احتفظ بنسخة من الملفات الأصلية
- احفظ النتائج في مجلد منفصل
- لا تحذف الملفات الأصلية حتى تتأكد من النتائج

## 📞 طلب المساعدة

إذا استمرت المشاكل:

1. **تشغيل الاختبار:**
   ```bash
   python test_gui.py
   ```

2. **فحص سجل الأخطاء:**
   - راجع سجل العمليات في الواجهة الرسومية
   - ابحث عن رسائل الخطأ المفصلة

3. **جمع معلومات النظام:**
   - إصدار Python
   - نظام التشغيل
   - مواصفات الحاسوب
   - نوع وحجم الملف المشكل

4. **تجربة البدائل:**
   - استخدم سطر الأوامر بدلاً من الواجهة الرسومية
   - جرب ملفات مختلفة للتأكد من المشكلة
   - استخدم العرض التجريبي: `python demo.py`

---

**تذكر:** معظم المشاكل تحل بإعادة تسمية الملفات وتبسيط المسارات!
