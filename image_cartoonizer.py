"""
فئة لتحويل الصور إلى كرتون باستخدام OpenCV
"""

import cv2
import numpy as np
from typing import Dict, Any
from config import CARTOON_STYLES, STYLE_DESCRIPTIONS

class ImageCartoonizer:
    """فئة لتحويل الصور إلى كرتون"""

    def __init__(self, style: str = 'classic'):
        """
        تهيئة محول الصور إلى كرتون

        Args:
            style: نمط الكرتون ('classic', 'smooth', 'sharp')
        """
        if style not in CARTOON_STYLES:
            raise ValueError(f"النمط غير مدعوم: {style}. الأنماط المدعومة: {list(CARTOON_STYLES.keys())}")

        self.style = style
        self.params = CARTOON_STYLES[style]

    def cartoonize_image(self, image: np.ndarray) -> np.ndarray:
        """
        تحويل صورة واحدة إلى كرتون

        Args:
            image: الصورة الأصلية

        Returns:
            الصورة المحولة إلى كرتون
        """
        # الخطوة 1: تطبيق Bilateral Filter لتنعيم الصورة مع الحفاظ على الحواف
        smooth = cv2.bilateralFilter(
            image,
            self.params['bilateral_d'],
            self.params['bilateral_sigma_color'],
            self.params['bilateral_sigma_space']
        )

        # الخطوة 2: تحسين الألوان والتباين (للأنماط المتقدمة)
        if self.params.get('use_color_enhancement', False):
            smooth = self._enhance_colors(smooth)

        # الخطوة 3: تحويل إلى رمادي لاستخراج الحواف
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray_blur = cv2.medianBlur(gray, self.params['median_blur_value'])

        # الخطوة 4: استخراج الحواف باستخدام Adaptive Threshold
        edges = cv2.adaptiveThreshold(
            gray_blur, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY,
            self.params['adaptive_threshold_block_size'],
            self.params['adaptive_threshold_c']
        )

        # الخطوة 5: تحسين سماكة الحواف
        edge_thickness = self.params.get('edge_thickness', 2)
        if edge_thickness > 1:
            kernel = np.ones((edge_thickness, edge_thickness), np.uint8)
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

        # الخطوة 6: تحويل الحواف إلى 3 قنوات
        edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)

        # الخطوة 7: تقليل عدد الألوان باستخدام K-means
        cartoon = self._reduce_colors(smooth, self.params['num_colors'])

        # الخطوة 8: تطبيق التأثيرات الخاصة حسب النمط
        cartoon = self._apply_style_effects(cartoon)

        # الخطوة 9: دمج الحواف مع الصورة المنعمة
        cartoon = cv2.bitwise_and(cartoon, edges)

        # الخطوة 10: تحسينات نهائية
        cartoon = self._apply_final_enhancements(cartoon)

        return cartoon

    def _reduce_colors(self, image: np.ndarray, k: int) -> np.ndarray:
        """
        تقليل عدد الألوان في الصورة باستخدام K-means clustering

        Args:
            image: الصورة الأصلية
            k: عدد الألوان المطلوب

        Returns:
            الصورة مع عدد ألوان مقلل
        """
        # تحويل الصورة إلى مصفوفة ثنائية الأبعاد
        data = image.reshape((-1, 3))
        data = np.float32(data)

        # تطبيق K-means
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
        _, labels, centers = cv2.kmeans(data, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)

        # تحويل النتائج إلى uint8
        centers = np.uint8(centers)

        # إعادة بناء الصورة
        segmented_data = centers[labels.flatten()]
        segmented_image = segmented_data.reshape(image.shape)

        return segmented_image

    def apply_edge_enhancement(self, image: np.ndarray) -> np.ndarray:
        """
        تحسين الحواف في الصورة

        Args:
            image: الصورة الأصلية

        Returns:
            الصورة مع حواف محسنة
        """
        # تحويل إلى رمادي
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # تطبيق Gaussian blur
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)

        # استخراج الحواف باستخدام Canny
        edges = cv2.Canny(blurred, 50, 150)

        # تحويل إلى 3 قنوات
        edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)

        # دمج مع الصورة الأصلية
        result = cv2.addWeighted(image, 0.8, edges, 0.2, 0)

        return result

    def apply_color_quantization(self, image: np.ndarray, levels: int = 8) -> np.ndarray:
        """
        تطبيق تكميم الألوان

        Args:
            image: الصورة الأصلية
            levels: عدد مستويات الألوان

        Returns:
            الصورة مع ألوان مكممة
        """
        # تقسيم القيم إلى مستويات
        factor = 255 // (levels - 1)
        quantized = (image // factor) * factor

        return quantized.astype(np.uint8)

    def _enhance_colors(self, image: np.ndarray) -> np.ndarray:
        """تحسين الألوان والتباين"""
        # تحويل إلى HSV لتحسين التشبع
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        hsv = hsv.astype(np.float32)

        # تحسين التشبع
        saturation_boost = self.params.get('color_saturation', 1.0)
        hsv[:, :, 1] = hsv[:, :, 1] * saturation_boost
        hsv[:, :, 1] = np.clip(hsv[:, :, 1], 0, 255)

        # تحسين السطوع
        brightness_boost = self.params.get('brightness_boost', 1.0)
        hsv[:, :, 2] = hsv[:, :, 2] * brightness_boost
        hsv[:, :, 2] = np.clip(hsv[:, :, 2], 0, 255)

        # تحويل العودة إلى BGR
        hsv = hsv.astype(np.uint8)
        enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2BGR)

        # تحسين التباين إذا لزم الأمر
        contrast_boost = self.params.get('contrast_boost', 1.0)
        if contrast_boost != 1.0:
            enhanced = cv2.convertScaleAbs(enhanced, alpha=contrast_boost, beta=0)

        return enhanced

    def _apply_style_effects(self, image: np.ndarray) -> np.ndarray:
        """تطبيق التأثيرات الخاصة حسب النمط"""
        result = image.copy()

        # تأثير الجلد للأنمي
        if self.params.get('use_skin_detection', False):
            result = self._enhance_skin_tones(result)

        # تنعيم التدرجات لديزني
        if self.params.get('use_gradient_smoothing', False):
            result = self._smooth_gradients(result)

        # تحسين الظلال لبيكسار
        if self.params.get('use_shadow_enhancement', False):
            result = self._enhance_shadows(result)

        # تأثير النقاط النصفية للكوميك
        if self.params.get('use_halftone_effect', False):
            result = self._apply_halftone_effect(result)

        # تأثير النسيج للألوان المائية
        if self.params.get('use_texture_effect', False):
            result = self._apply_texture_effect(result)

        # تأثير ضربات الفرشاة للرسم الزيتي
        if self.params.get('use_brush_strokes', False):
            result = self._apply_brush_strokes(result)

        # تأثير السيبيا للكرتون القديم
        if self.params.get('use_sepia_tone', False):
            result = self._apply_sepia_tone(result)

        # تأثير حبيبات الفيلم
        if self.params.get('use_film_grain', False):
            result = self._apply_film_grain(result)

        # تأثير التوهج للنيون
        if self.params.get('use_glow_effect', False):
            result = self._apply_glow_effect(result)

        # تباين عالي للنيون
        if self.params.get('use_high_contrast', False):
            result = self._apply_high_contrast(result)

        # حواف ناعمة للألوان المائية
        if self.params.get('use_soft_edges', False):
            result = self._apply_soft_edges(result)

        return result

    def _apply_final_enhancements(self, image: np.ndarray) -> np.ndarray:
        """تطبيق التحسينات النهائية"""
        result = image.copy()

        # تحسين عام للألوان
        if hasattr(self, 'params'):
            # تطبيق تحسين السطوع النهائي
            brightness = self.params.get('brightness_boost', 1.0)
            if brightness != 1.0:
                result = cv2.convertScaleAbs(result, alpha=1.0, beta=(brightness - 1.0) * 30)

            # تطبيق تحسين التباين النهائي
            contrast = self.params.get('contrast_boost', 1.0)
            if contrast != 1.0:
                result = cv2.convertScaleAbs(result, alpha=contrast, beta=0)

        return result

    def _enhance_skin_tones(self, image: np.ndarray) -> np.ndarray:
        """تحسين ألوان البشرة للأنمي"""
        # تحويل إلى HSV
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # تحديد نطاق ألوان البشرة
        lower_skin = np.array([0, 20, 70], dtype=np.uint8)
        upper_skin = np.array([20, 255, 255], dtype=np.uint8)

        # إنشاء قناع للبشرة
        skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)

        # تنعيم القناع
        skin_mask = cv2.medianBlur(skin_mask, 5)

        # تطبيق تنعيم إضافي على مناطق البشرة
        skin_smooth = cv2.bilateralFilter(image, 15, 80, 80)

        # دمج النتيجة
        result = image.copy()
        result[skin_mask > 0] = skin_smooth[skin_mask > 0]

        return result

    def get_available_styles(self) -> list:
        """الحصول على قائمة الأنماط المتاحة"""
        return list(CARTOON_STYLES.keys())

    def get_style_description(self, style: str) -> str:
        """الحصول على وصف النمط"""
        return STYLE_DESCRIPTIONS.get(style, "")

    def set_style(self, style: str):
        """تغيير نمط الكرتون"""
        if style not in CARTOON_STYLES:
            raise ValueError(f"النمط غير مدعوم: {style}")

        self.style = style
        self.params = CARTOON_STYLES[style]

    def _smooth_gradients(self, image: np.ndarray) -> np.ndarray:
        """تنعيم التدرجات لنمط ديزني"""
        # تطبيق تنعيم متقدم
        smooth = cv2.edgePreservingFilter(image, flags=2, sigma_s=50, sigma_r=0.4)
        return smooth

    def _enhance_shadows(self, image: np.ndarray) -> np.ndarray:
        """تحسين الظلال لنمط بيكسار"""
        # تحويل إلى LAB للعمل على الإضاءة
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)

        # تطبيق CLAHE على قناة الإضاءة
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)

        # دمج القنوات
        enhanced_lab = cv2.merge([l, a, b])
        result = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)

        return result

    def _apply_halftone_effect(self, image: np.ndarray) -> np.ndarray:
        """تطبيق تأثير النقاط النصفية للكوميك"""
        # تحويل إلى رمادي
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # إنشاء نمط النقاط
        height, width = gray.shape
        dot_size = 4

        # إنشاء قناع النقاط
        dots = np.zeros_like(gray)
        for y in range(0, height, dot_size * 2):
            for x in range(0, width, dot_size * 2):
                if y + dot_size < height and x + dot_size < width:
                    intensity = gray[y:y+dot_size, x:x+dot_size].mean()
                    if intensity > 128:
                        cv2.circle(dots, (x + dot_size//2, y + dot_size//2),
                                 dot_size//2, 255, -1)

        # تطبيق التأثير
        dots_colored = cv2.cvtColor(dots, cv2.COLOR_GRAY2BGR)
        result = cv2.bitwise_and(image, dots_colored)

        return result

    def _apply_texture_effect(self, image: np.ndarray) -> np.ndarray:
        """تطبيق تأثير النسيج للألوان المائية"""
        # إنشاء نسيج عشوائي
        height, width = image.shape[:2]
        noise = np.random.randint(0, 50, (height, width), dtype=np.uint8)
        noise = cv2.GaussianBlur(noise, (3, 3), 0)

        # تطبيق النسيج
        texture = cv2.cvtColor(noise, cv2.COLOR_GRAY2BGR)
        result = cv2.addWeighted(image, 0.9, texture, 0.1, 0)

        return result

    def _apply_brush_strokes(self, image: np.ndarray) -> np.ndarray:
        """تطبيق تأثير ضربات الفرشاة للرسم الزيتي"""
        # تطبيق تنعيم اتجاهي
        kernel_h = np.array([[0, -1, 0], [0, 3, 0], [0, -1, 0]])
        kernel_v = np.array([[0, 0, 0], [-1, 3, -1], [0, 0, 0]])

        # تطبيق الفلاتر
        stroke_h = cv2.filter2D(image, -1, kernel_h)
        stroke_v = cv2.filter2D(image, -1, kernel_v)

        # دمج التأثيرات
        result = cv2.addWeighted(stroke_h, 0.5, stroke_v, 0.5, 0)

        return result

    def _apply_sepia_tone(self, image: np.ndarray) -> np.ndarray:
        """تطبيق تأثير السيبيا للكرتون القديم"""
        # مصفوفة تحويل السيبيا
        sepia_filter = np.array([[0.272, 0.534, 0.131],
                                [0.349, 0.686, 0.168],
                                [0.393, 0.769, 0.189]])

        # تطبيق التحويل
        sepia_img = image.dot(sepia_filter.T)
        sepia_img = np.clip(sepia_img, 0, 255).astype(np.uint8)

        return sepia_img

    def _apply_film_grain(self, image: np.ndarray) -> np.ndarray:
        """تطبيق تأثير حبيبات الفيلم"""
        # إنشاء ضوضاء
        height, width = image.shape[:2]
        noise = np.random.normal(0, 25, (height, width, 3))

        # تطبيق الضوضاء
        noisy = image.astype(np.float32) + noise
        result = np.clip(noisy, 0, 255).astype(np.uint8)

        return result

    def _apply_glow_effect(self, image: np.ndarray) -> np.ndarray:
        """تطبيق تأثير التوهج للنيون"""
        # إنشاء قناع التوهج
        glow = cv2.GaussianBlur(image, (15, 15), 0)

        # دمج مع الصورة الأصلية
        result = cv2.addWeighted(image, 0.7, glow, 0.3, 0)

        return result

    def _apply_high_contrast(self, image: np.ndarray) -> np.ndarray:
        """تطبيق تباين عالي للنيون"""
        # تحسين التباين
        result = cv2.convertScaleAbs(image, alpha=1.5, beta=-50)

        return result

    def _apply_soft_edges(self, image: np.ndarray) -> np.ndarray:
        """تطبيق حواف ناعمة للألوان المائية"""
        # تنعيم الحواف
        blurred = cv2.GaussianBlur(image, (5, 5), 0)
        result = cv2.addWeighted(image, 0.6, blurred, 0.4, 0)

        return result