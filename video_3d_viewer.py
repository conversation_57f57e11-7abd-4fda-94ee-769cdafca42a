#!/usr/bin/env python3
"""
محرك العرض ثلاثي الأبعاد للفيديو مثل Chroma Studio
"""

import tkinter as tk
from tkinter import ttk
import numpy as np
import cv2
import threading
import time
import math
from PIL import Image, ImageTk

class Video3DViewer:
    """محرك العرض ثلاثي الأبعاد للفيديو"""
    
    def __init__(self, parent_frame, width=800, height=600):
        self.parent_frame = parent_frame
        self.width = width
        self.height = height
        
        # متغيرات التحكم في العرض
        self.rotation_x = 0
        self.rotation_y = 0
        self.rotation_z = 0
        self.zoom = 1.0
        self.speed_multiplier = 1.0
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        
        # متغيرات الفيديو
        self.video_frames = []
        self.original_frames = []
        self.cartoon_frames = []
        self.fps = 30
        
        # متغيرات التفاعل
        self.mouse_x = 0
        self.mouse_y = 0
        self.mouse_pressed = False
        
        # إعداد الواجهة
        self.setup_3d_interface()
        
        # بدء حلقة الرسم
        self.animation_thread = None
        self.running = True
        
    def setup_3d_interface(self):
        """إعداد واجهة العرض ثلاثي الأبعاد"""
        # الإطار الرئيسي للعرض ثلاثي الأبعاد
        self.main_3d_frame = ttk.Frame(self.parent_frame)
        self.main_3d_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إطار العرض
        self.viewer_frame = ttk.LabelFrame(self.main_3d_frame, text="العرض ثلاثي الأبعاد", padding="10")
        self.viewer_frame.pack(fill=tk.BOTH, expand=True)
        
        # Canvas للعرض ثلاثي الأبعاد
        self.canvas_3d = tk.Canvas(
            self.viewer_frame, 
            width=self.width, 
            height=self.height,
            bg='#1a1a1a',
            highlightthickness=0
        )
        self.canvas_3d.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # ربط أحداث الماوس
        self.canvas_3d.bind("<Button-1>", self.on_mouse_press)
        self.canvas_3d.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas_3d.bind("<ButtonRelease-1>", self.on_mouse_release)
        self.canvas_3d.bind("<MouseWheel>", self.on_mouse_wheel)
        
        # إطار التحكم
        self.setup_control_panel()
        
    def setup_control_panel(self):
        """إعداد لوحة التحكم"""
        # إطار التحكم الجانبي
        self.control_frame = ttk.Frame(self.viewer_frame, width=250)
        self.control_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        self.control_frame.pack_propagate(False)
        
        # عنوان التحكم
        ttk.Label(self.control_frame, text="التحكم في العرض", 
                 font=('Arial', 12, 'bold')).pack(pady=(0, 10))
        
        # تحكم الدوران
        rotation_frame = ttk.LabelFrame(self.control_frame, text="الدوران", padding="5")
        rotation_frame.pack(fill=tk.X, pady=(0, 10))
        
        # دوران X
        ttk.Label(rotation_frame, text="دوران X:").pack(anchor=tk.W)
        self.rotation_x_var = tk.DoubleVar(value=0)
        self.rotation_x_scale = ttk.Scale(
            rotation_frame, from_=-180, to=180, 
            variable=self.rotation_x_var, orient=tk.HORIZONTAL,
            command=self.on_rotation_change
        )
        self.rotation_x_scale.pack(fill=tk.X, pady=(0, 5))
        
        # دوران Y
        ttk.Label(rotation_frame, text="دوران Y:").pack(anchor=tk.W)
        self.rotation_y_var = tk.DoubleVar(value=0)
        self.rotation_y_scale = ttk.Scale(
            rotation_frame, from_=-180, to=180,
            variable=self.rotation_y_var, orient=tk.HORIZONTAL,
            command=self.on_rotation_change
        )
        self.rotation_y_scale.pack(fill=tk.X, pady=(0, 5))
        
        # دوران Z
        ttk.Label(rotation_frame, text="دوران Z:").pack(anchor=tk.W)
        self.rotation_z_var = tk.DoubleVar(value=0)
        self.rotation_z_scale = ttk.Scale(
            rotation_frame, from_=-180, to=180,
            variable=self.rotation_z_var, orient=tk.HORIZONTAL,
            command=self.on_rotation_change
        )
        self.rotation_z_scale.pack(fill=tk.X)
        
        # تحكم التكبير
        zoom_frame = ttk.LabelFrame(self.control_frame, text="التكبير", padding="5")
        zoom_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(zoom_frame, text="مستوى التكبير:").pack(anchor=tk.W)
        self.zoom_var = tk.DoubleVar(value=1.0)
        self.zoom_scale = ttk.Scale(
            zoom_frame, from_=0.1, to=3.0,
            variable=self.zoom_var, orient=tk.HORIZONTAL,
            command=self.on_zoom_change
        )
        self.zoom_scale.pack(fill=tk.X)
        
        # تحكم السرعة
        speed_frame = ttk.LabelFrame(self.control_frame, text="السرعة", padding="5")
        speed_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(speed_frame, text="سرعة التشغيل:").pack(anchor=tk.W)
        self.speed_var = tk.DoubleVar(value=1.0)
        self.speed_scale = ttk.Scale(
            speed_frame, from_=0.1, to=5.0,
            variable=self.speed_var, orient=tk.HORIZONTAL,
            command=self.on_speed_change
        )
        self.speed_scale.pack(fill=tk.X)
        
        # أزرار التحكم في التشغيل
        playback_frame = ttk.LabelFrame(self.control_frame, text="التشغيل", padding="5")
        playback_frame.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار التحكم
        button_frame = ttk.Frame(playback_frame)
        button_frame.pack(fill=tk.X)
        
        self.play_button = ttk.Button(button_frame, text="▶", command=self.toggle_playback, width=3)
        self.play_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(button_frame, text="⏹", command=self.stop_playback, width=3).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="⏮", command=self.previous_frame, width=3).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="⏭", command=self.next_frame, width=3).pack(side=tk.LEFT)
        
        # شريط تقدم الفيديو
        ttk.Label(playback_frame, text="موضع الفيديو:").pack(anchor=tk.W, pady=(10, 0))
        self.frame_var = tk.IntVar(value=0)
        self.frame_scale = ttk.Scale(
            playback_frame, from_=0, to=100,
            variable=self.frame_var, orient=tk.HORIZONTAL,
            command=self.on_frame_change
        )
        self.frame_scale.pack(fill=tk.X)
        
        # معلومات الإطار
        self.frame_info_label = ttk.Label(playback_frame, text="الإطار: 0 / 0")
        self.frame_info_label.pack(anchor=tk.W, pady=(5, 0))
        
        # أزرار إعادة التعيين
        reset_frame = ttk.Frame(self.control_frame)
        reset_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(reset_frame, text="إعادة تعيين الدوران", 
                  command=self.reset_rotation).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(reset_frame, text="إعادة تعيين التكبير", 
                  command=self.reset_zoom).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(reset_frame, text="إعادة تعيين الكل", 
                  command=self.reset_all).pack(fill=tk.X)
        
    def load_video_frames(self, video_path):
        """تحميل إطارات الفيديو"""
        try:
            cap = cv2.VideoCapture(video_path)
            self.fps = cap.get(cv2.CAP_PROP_FPS)
            self.total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            self.original_frames = []
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # تحويل من BGR إلى RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                self.original_frames.append(frame_rgb)
            
            cap.release()
            
            # تحديث شريط التقدم
            self.frame_scale.configure(to=self.total_frames - 1)
            self.update_frame_info()
            
            # بدء العرض
            self.start_3d_rendering()
            
            return True
            
        except Exception as e:
            print(f"خطأ في تحميل الفيديو: {e}")
            return False
    
    def start_3d_rendering(self):
        """بدء العرض ثلاثي الأبعاد"""
        if self.animation_thread is None or not self.animation_thread.is_alive():
            self.running = True
            self.animation_thread = threading.Thread(target=self.render_loop, daemon=True)
            self.animation_thread.start()
    
    def render_loop(self):
        """حلقة الرسم الرئيسية"""
        while self.running:
            try:
                if self.original_frames and self.current_frame < len(self.original_frames):
                    self.render_3d_frame()
                
                # تحديث الإطار إذا كان التشغيل مفعل
                if self.is_playing:
                    self.current_frame += int(self.speed_multiplier)
                    if self.current_frame >= len(self.original_frames):
                        self.current_frame = 0
                    
                    # تحديث واجهة المستخدم
                    self.parent_frame.after(0, self.update_ui)
                
                # تحكم في معدل الإطارات
                time.sleep(1.0 / (self.fps * self.speed_multiplier) if self.is_playing else 0.033)
                
            except Exception as e:
                print(f"خطأ في حلقة الرسم: {e}")
                time.sleep(0.1)
    
    def render_3d_frame(self):
        """رسم الإطار ثلاثي الأبعاد"""
        if not self.original_frames or self.current_frame >= len(self.original_frames):
            return
        
        try:
            # الحصول على الإطار الحالي
            current_image = self.original_frames[self.current_frame]
            
            # تطبيق التحويلات ثلاثية الأبعاد
            transformed_image = self.apply_3d_transform(current_image)
            
            # تحويل إلى PhotoImage وعرض
            pil_image = Image.fromarray(transformed_image)
            photo = ImageTk.PhotoImage(pil_image)
            
            # تحديث Canvas
            self.parent_frame.after(0, lambda: self.update_canvas(photo))
            
        except Exception as e:
            print(f"خطأ في رسم الإطار: {e}")
    
    def apply_3d_transform(self, image):
        """تطبيق التحويلات ثلاثية الأبعاد على الصورة"""
        height, width = image.shape[:2]
        
        # إنشاء مصفوفة التحويل
        center_x, center_y = width // 2, height // 2
        
        # مصفوفات الدوران
        rx = math.radians(self.rotation_x)
        ry = math.radians(self.rotation_y)
        rz = math.radians(self.rotation_z)
        
        # تطبيق تأثير المنظور ثلاثي الأبعاد
        transformed_image = self.apply_perspective_transform(image, rx, ry, rz)
        
        # تطبيق التكبير
        if self.zoom != 1.0:
            new_width = int(width * self.zoom)
            new_height = int(height * self.zoom)
            transformed_image = cv2.resize(transformed_image, (new_width, new_height))
        
        # تأكد من أن الصورة تناسب Canvas
        canvas_width = self.canvas_3d.winfo_width() or self.width
        canvas_height = self.canvas_3d.winfo_height() or self.height
        
        # تغيير الحجم ليناسب Canvas
        img_height, img_width = transformed_image.shape[:2]
        scale = min(canvas_width / img_width, canvas_height / img_height)
        
        if scale < 1.0:
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            transformed_image = cv2.resize(transformed_image, (new_width, new_height))
        
        return transformed_image
    
    def apply_perspective_transform(self, image, rx, ry, rz):
        """تطبيق تحويل المنظور ثلاثي الأبعاد"""
        height, width = image.shape[:2]
        
        # نقاط الزوايا الأصلية
        corners = np.float32([
            [0, 0],
            [width, 0],
            [width, height],
            [0, height]
        ])
        
        # تطبيق الدوران ثلاثي الأبعاد
        center_x, center_y = width // 2, height // 2
        
        # حساب النقاط الجديدة بعد الدوران
        new_corners = []
        for corner in corners:
            x, y = corner[0] - center_x, corner[1] - center_y
            
            # دوران حول المحور X
            y_new = y * math.cos(rx) - 0 * math.sin(rx)
            z_new = y * math.sin(rx) + 0 * math.cos(rx)
            
            # دوران حول المحور Y
            x_new = x * math.cos(ry) + z_new * math.sin(ry)
            z_new = -x * math.sin(ry) + z_new * math.cos(ry)
            
            # دوران حول المحور Z
            x_final = x_new * math.cos(rz) - y_new * math.sin(rz)
            y_final = x_new * math.sin(rz) + y_new * math.cos(rz)
            
            # تطبيق المنظور
            perspective_factor = 1.0 + z_new / 1000.0
            x_final /= perspective_factor
            y_final /= perspective_factor
            
            new_corners.append([x_final + center_x, y_final + center_y])
        
        new_corners = np.float32(new_corners)
        
        # تطبيق التحويل
        try:
            matrix = cv2.getPerspectiveTransform(corners, new_corners)
            transformed = cv2.warpPerspective(image, matrix, (width, height))
            return transformed
        except:
            return image
    
    def update_canvas(self, photo):
        """تحديث Canvas بالصورة الجديدة"""
        try:
            self.canvas_3d.delete("all")
            
            canvas_width = self.canvas_3d.winfo_width()
            canvas_height = self.canvas_3d.winfo_height()
            
            self.canvas_3d.create_image(
                canvas_width // 2, canvas_height // 2, 
                image=photo, anchor=tk.CENTER
            )
            
            # حفظ مرجع للصورة
            self.canvas_3d.image = photo
            
        except Exception as e:
            print(f"خطأ في تحديث Canvas: {e}")
    
    def update_ui(self):
        """تحديث واجهة المستخدم"""
        self.frame_var.set(self.current_frame)
        self.update_frame_info()
    
    def update_frame_info(self):
        """تحديث معلومات الإطار"""
        self.frame_info_label.config(text=f"الإطار: {self.current_frame + 1} / {self.total_frames}")
    
    # أحداث التحكم
    def on_rotation_change(self, value=None):
        """عند تغيير الدوران"""
        self.rotation_x = self.rotation_x_var.get()
        self.rotation_y = self.rotation_y_var.get()
        self.rotation_z = self.rotation_z_var.get()
    
    def on_zoom_change(self, value=None):
        """عند تغيير التكبير"""
        self.zoom = self.zoom_var.get()
    
    def on_speed_change(self, value=None):
        """عند تغيير السرعة"""
        self.speed_multiplier = self.speed_var.get()
    
    def on_frame_change(self, value=None):
        """عند تغيير موضع الإطار"""
        self.current_frame = self.frame_var.get()
        self.update_frame_info()
    
    # أحداث الماوس
    def on_mouse_press(self, event):
        """عند الضغط على الماوس"""
        self.mouse_x = event.x
        self.mouse_y = event.y
        self.mouse_pressed = True
    
    def on_mouse_drag(self, event):
        """عند سحب الماوس"""
        if self.mouse_pressed:
            dx = event.x - self.mouse_x
            dy = event.y - self.mouse_y
            
            # تحديث الدوران بناءً على حركة الماوس
            self.rotation_y_var.set(self.rotation_y_var.get() + dx * 0.5)
            self.rotation_x_var.set(self.rotation_x_var.get() + dy * 0.5)
            
            self.on_rotation_change()
            
            self.mouse_x = event.x
            self.mouse_y = event.y
    
    def on_mouse_release(self, event):
        """عند تحرير الماوس"""
        self.mouse_pressed = False
    
    def on_mouse_wheel(self, event):
        """عند تحريك عجلة الماوس"""
        zoom_delta = 0.1 if event.delta > 0 else -0.1
        new_zoom = max(0.1, min(3.0, self.zoom_var.get() + zoom_delta))
        self.zoom_var.set(new_zoom)
        self.on_zoom_change()
    
    # أزرار التحكم
    def toggle_playback(self):
        """تبديل التشغيل/الإيقاف"""
        self.is_playing = not self.is_playing
        self.play_button.config(text="⏸" if self.is_playing else "▶")
    
    def stop_playback(self):
        """إيقاف التشغيل"""
        self.is_playing = False
        self.current_frame = 0
        self.play_button.config(text="▶")
        self.update_ui()
    
    def previous_frame(self):
        """الإطار السابق"""
        if self.current_frame > 0:
            self.current_frame -= 1
            self.update_ui()
    
    def next_frame(self):
        """الإطار التالي"""
        if self.current_frame < self.total_frames - 1:
            self.current_frame += 1
            self.update_ui()
    
    # إعادة التعيين
    def reset_rotation(self):
        """إعادة تعيين الدوران"""
        self.rotation_x_var.set(0)
        self.rotation_y_var.set(0)
        self.rotation_z_var.set(0)
        self.on_rotation_change()
    
    def reset_zoom(self):
        """إعادة تعيين التكبير"""
        self.zoom_var.set(1.0)
        self.on_zoom_change()
    
    def reset_all(self):
        """إعادة تعيين جميع الإعدادات"""
        self.reset_rotation()
        self.reset_zoom()
        self.speed_var.set(1.0)
        self.on_speed_change()
    
    def cleanup(self):
        """تنظيف الموارد"""
        self.running = False
        if self.animation_thread and self.animation_thread.is_alive():
            self.animation_thread.join(timeout=1.0)
