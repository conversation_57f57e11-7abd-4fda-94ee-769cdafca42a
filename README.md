# برنامج تحويل الفيديو إلى كرتون بالذكاء الاصطناعي

برنامج متقدم لتحويل مقاطع الفيديو والصور إلى كرتون باستخدام تقنيات الذكاء الاصطناعي ومعالجة الصور.

## الميزات

- ✨ تحويل مقاطع الفيديو الكاملة إلى كرتون
- 🖼️ تحويل الصور الفردية إلى كرتون
- 🎨 ثلاثة أنماط مختلفة للكرتون (كلاسيكي، ناعم، حاد)
- ✂️ إمكانية تحويل مقاطع محددة من الفيديو
- 📊 شريط تقدم مفصل أثناء المعالجة
- 🔧 إعدادات قابلة للتخصيص
- 🌍 واجهة باللغة العربية

## التقنيات المستخدمة

- **OpenCV**: لمعالجة الفيديو والصور
- **NumPy**: للعمليات الرياضية
- **Bilateral Filter**: لتنعيم الصورة مع الحفاظ على الحواف
- **Edge Detection**: لاستخراج الحواف
- **K-means Clustering**: لتقليل الألوان
- **Adaptive Thresholding**: لتحسين الحواف

## التثبيت

1. تأكد من وجود Python 3.7 أو أحدث:
```bash
python --version
```

2. قم بتثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

## الاستخدام

### تحويل فيديو كامل
```bash
python main.py input_video.mp4
```

### تحويل بنمط محدد
```bash
python main.py input_video.mp4 --style smooth
```

### تحويل مقطع محدد (من الثانية 10 لمدة 30 ثانية)
```bash
python main.py input_video.mp4 --segment 10 30
```

### تحويل صورة واحدة
```bash
python main.py --image photo.jpg
```

### تحديد ملف الإخراج
```bash
python main.py input_video.mp4 --output cartoon_video.mp4
```

### عرض معلومات الفيديو
```bash
python main.py input_video.mp4 --info
```

### عرض الأنماط المتاحة
```bash
python main.py --list-styles
```

## الأنماط المتاحة

### 1. كلاسيكي (classic)
- النمط الافتراضي
- توازن جيد بين التفاصيل والتبسيط
- مناسب لمعظم أنواع الفيديو

### 2. ناعم (smooth)
- تنعيم أكثر للصورة
- ألوان أكثر تدرجاً
- مناسب للمشاهد الطبيعية

### 3. حاد (sharp)
- حواف أكثر وضوحاً
- ألوان أقل تدرجاً
- مناسب للرسوم المتحركة والشخصيات

## هيكل المشروع

```
├── main.py                 # الملف الرئيسي
├── video_cartoonizer.py    # فئة تحويل الفيديو
├── image_cartoonizer.py    # فئة تحويل الصور
├── config.py              # إعدادات التطبيق
├── utils.py               # وظائف مساعدة
├── requirements.txt       # متطلبات المشروع
├── README.md             # دليل الاستخدام
├── input/                # مجلد الملفات الأصلية
├── output/               # مجلد النتائج
└── temp/                 # مجلد مؤقت
```

## أمثلة متقدمة

### معالجة متعددة الملفات
```bash
# معالجة جميع ملفات MP4 في مجلد
for file in *.mp4; do
    python main.py "$file" --style classic
done
```

### تحويل بجودة عالية
```bash
python main.py input.mp4 --style sharp --verbose
```

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في تثبيت OpenCV**:
```bash
pip install opencv-python --upgrade
```

2. **نفاد الذاكرة مع الفيديوهات الكبيرة**:
   - استخدم مقاطع أصغر مع `--segment`
   - قلل من دقة الفيديو

3. **بطء في المعالجة**:
   - استخدم النمط `classic` للسرعة
   - قم بمعالجة مقاطع أصغر

## المساهمة

نرحب بالمساهمات! يمكنك:
- إضافة أنماط جديدة للكرتون
- تحسين الأداء
- إضافة ميزات جديدة
- تحسين التوثيق

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح issue في المستودع.

---

**ملاحظة**: يتطلب هذا البرنامج موارد حاسوبية جيدة لمعالجة الفيديوهات عالية الدقة. للحصول على أفضل النتائج، استخدم فيديوهات بدقة 1080p أو أقل.
