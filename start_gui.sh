#!/bin/bash

echo "========================================"
echo "   محول الفيديو إلى كرتون"
echo "   Video to Cartoon Converter"
echo "========================================"
echo ""
echo "تشغيل الواجهة الرسومية..."
echo "Starting GUI..."
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "خطأ: Python غير مثبت على النظام"
        echo "Error: Python is not installed"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# تشغيل البرنامج
$PYTHON_CMD run_gui.py

# التحقق من حالة الخروج
if [ $? -ne 0 ]; then
    echo ""
    echo "خطأ في تشغيل البرنامج!"
    echo "Error running the program!"
    echo ""
    echo "يرجى التأكد من:"
    echo "Please make sure:"
    echo "1. Python مثبت على النظام - Python is installed"
    echo "2. تم تثبيت المتطلبات - Requirements are installed"
    echo "   pip install opencv-python numpy matplotlib Pillow tqdm"
    echo ""
    read -p "اضغط Enter للخروج - Press Enter to exit..."
fi
