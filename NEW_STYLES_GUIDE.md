# دليل الأنماط الجديدة - محول الفيديو إلى كرتون

## 🎨 الأنماط المتاحة الآن

تم إضافة **8 أنماط جديدة** إلى البرنامج، ليصبح المجموع **11 نمط** مختلف لتحويل الفيديو والصور إلى كرتون!

## 📊 جدول الأنماط الكامل

| النمط | الوصف | عدد الألوان | التشبع | السرعة | التأثيرات الخاصة |
|-------|--------|-------------|---------|---------|------------------|
| **classic** | متوازن - مناسب لمعظم الاستخدامات | 8 | 1.2x | متوسط | - |
| **smooth** | ناعم - مناسب للمناظر الطبيعية | 12 | 1.0x | بطيء | - |
| **sharp** | حاد - مناسب للشخصيات والرسوم | 6 | 1.4x | سريع | - |
| **anime** | أنمي - نمط الرسوم اليابانية | 6 | 1.6x | سريع | تحسين البشرة |
| **disney** | ديزني - نمط أفلام ديزني | 10 | 1.4x | متوسط | تنعيم التدرجات |
| **pixar** | بيكسار - نمط الرسوم ثلاثية الأبعاد | 8 | 1.3x | متوسط | تحسين الظلال |
| **comic** | كوميك - نمط الكتب المصورة | 5 | 1.8x | سريع جداً | نقاط نصفية |
| **watercolor** | ألوان مائية - نمط الرسم المائي | 15 | 0.9x | بطيء جداً | نسيج + حواف ناعمة |
| **oil_painting** | رسم زيتي - نمط اللوحات الزيتية | 12 | 1.2x | متوسط | نسيج + ضربات فرشاة |
| **vintage_cartoon** | كرتون قديم - نمط كلاسيكي | 7 | 0.8x | متوسط | سيبيا + حبيبات فيلم |
| **neon** | نيون - نمط مضيء فلورسنت | 4 | 2.0x | سريع | توهج + تباين عالي |

## 🎯 الأنماط الجديدة بالتفصيل

### 🌟 أنماط أفلام الكرتون

#### 1. **Anime (أنمي)**
- **الاستخدام المثالي**: الوجوه والشخصيات
- **المميزات**: 
  - ألوان زاهية ومشبعة
  - تحسين تلقائي لألوان البشرة
  - حواف واضحة ومحددة
- **مثالي لـ**: صور الأشخاص، الشخصيات الكرتونية

#### 2. **Disney (ديزني)**
- **الاستخدام المثالي**: المشاهد العائلية والطبيعة
- **المميزات**:
  - تنعيم متقدم للتدرجات
  - ألوان دافئة ومريحة
  - نعومة في الانتقالات
- **مثالي لـ**: مقاطع الأطفال، المناظر الطبيعية

#### 3. **Pixar (بيكسار)**
- **الاستخدام المثالي**: المشاهد ثلاثية الأبعاد
- **المميزات**:
  - تحسين الظلال والإضاءة
  - عمق بصري محسن
  - ألوان متوازنة
- **مثالي لـ**: المشاهد المعقدة، الكائنات ثلاثية الأبعاد

### 🎨 الأنماط الفنية

#### 4. **Comic (كوميك)**
- **الاستخدام المثالي**: الكتب المصورة والقصص
- **المميزات**:
  - حواف سميكة وواضحة
  - ألوان مشبعة جداً
  - تأثير النقاط النصفية
- **مثالي لـ**: القصص المصورة، الشخصيات الكرتونية

#### 5. **Watercolor (ألوان مائية)**
- **الاستخدام المثالي**: الفن التشكيلي والطبيعة
- **المميزات**:
  - نسيج مائي طبيعي
  - حواف ناعمة ومتدفقة
  - ألوان هادئة ومتدرجة
- **مثالي لـ**: المناظر الطبيعية، الفن التشكيلي

#### 6. **Oil Painting (رسم زيتي)**
- **الاستخدام المثالي**: البورتريه واللوحات الكلاسيكية
- **المميزات**:
  - نسيج زيتي واقعي
  - ضربات فرشاة واضحة
  - عمق لوني غني
- **مثالي لـ**: البورتريه، اللوحات الفنية

### ✨ الأنماط الخاصة

#### 7. **Vintage Cartoon (كرتون قديم)**
- **الاستخدام المثالي**: المحتوى الكلاسيكي والنوستالجي
- **المميزات**:
  - تأثير السيبيا الدافئ
  - حبيبات فيلم كلاسيكية
  - ألوان مكتومة وهادئة
- **مثالي لـ**: المحتوى التاريخي، الذكريات

#### 8. **Neon (نيون)**
- **الاستخدام المثالي**: المحتوى المستقبلي والحديث
- **المميزات**:
  - ألوان فلورسنت مضيئة
  - تأثير توهج مميز
  - تباين عالي وحاد
- **مثالي لـ**: المحتوى المستقبلي، الألعاب، الموسيقى

## 🚀 كيفية الاستخدام

### في الواجهة الرسومية:
1. افتح البرنامج: `python run_gui.py`
2. اختر الملف المراد تحويله
3. اختر النمط من القائمة المنسدلة
4. انقر على "معاينة سريعة" لرؤية النتيجة
5. انقر على "بدء التحويل"

### في سطر الأوامر:
```bash
# نمط الأنمي
python main.py photo.jpg --style anime

# نمط ديزني
python main.py video.mp4 --style disney

# نمط الكوميك
python main.py image.png --style comic

# نمط النيون
python main.py video.mp4 --style neon
```

## 📈 مقارنة الأداء

### السرعة (من الأسرع إلى الأبطأ):
1. **Comic** - 0.71 ثانية ⚡
2. **Anime** - 0.97 ثانية ⚡
3. **Sharp** - 1.08 ثانية 🔥
4. **Classic** - 1.50 ثانية ✅
5. **Pixar** - 1.78 ثانية ✅
6. **Disney** - 2.47 ثانية ⏳
7. **Smooth** - 2.94 ثانية ⏳
8. **Watercolor** - 3.78 ثانية 🐌

### جودة النتائج:
- **للوجوه**: Anime > Disney > Pixar
- **للطبيعة**: Watercolor > Disney > Smooth
- **للشخصيات**: Comic > Anime > Sharp
- **للفن**: Oil Painting > Watercolor > Vintage

## 🎨 نصائح للحصول على أفضل النتائج

### لكل نمط:

#### **Anime**:
- مثالي للصور التي تحتوي على وجوه
- يعمل بشكل رائع مع الإضاءة الجيدة
- أفضل مع الألوان الزاهية

#### **Disney**:
- ممتاز للمشاهد العائلية
- يحتاج إضاءة متوازنة
- أفضل مع المناظر الواسعة

#### **Comic**:
- مثالي للشخصيات والكائنات
- يعمل جيداً مع التباين العالي
- أفضل مع الخطوط الواضحة

#### **Neon**:
- مثالي للمشاهد الليلية
- يعمل رائع مع الألوان الداكنة
- أفضل مع الإضاءة الاصطناعية

## 🔧 إعدادات متقدمة

كل نمط له إعدادات خاصة يمكن تخصيصها في ملف `config.py`:

```python
'anime': {
    'num_colors': 6,           # عدد الألوان
    'color_saturation': 1.6,   # تشبع الألوان
    'edge_thickness': 4,       # سماكة الحواف
    'use_skin_detection': True # تحسين البشرة
}
```

## 🎉 الخلاصة

الآن لديك **11 نمط مختلف** لتحويل الفيديو والصور إلى كرتون:

✅ **3 أنماط أساسية** للاستخدام العام  
✅ **3 أنماط أفلام كرتون** للمحتوى الاحترافي  
✅ **3 أنماط فنية** للإبداع والفن  
✅ **2 نمط خاص** للتأثيرات المميزة  

كل نمط مصمم لحالة استخدام محددة ويقدم نتائج مميزة تناسب احتياجاتك!

---

**💡 نصيحة**: جرب أنماط مختلفة على نفس الصورة لاختيار الأنسب لمشروعك!
