# برنامج تحويل الفيديو إلى كرتون بالذكاء الاصطناعي

برنامج متقدم لتحويل مقاطع الفيديو والصور إلى كرتون باستخدام تقنيات الذكاء الاصطناعي ومعالجة الصور.

## الميزات

- ✨ تحويل مقاطع الفيديو الكاملة إلى كرتون
- 🖼️ تحويل الصور الفردية إلى كرتون
- 🎨 ثلاثة أنماط مختلفة للكرتون (كلاسيكي، ناعم، حاد)
- ✂️ إمكانية تحويل مقاطع محددة من الفيديو
- 📊 شريط تقدم مفصل أثناء المعالجة
- 🔧 إعدادات قابلة للتخصيص
- 🌍 واجهة باللغة العربية

## التقنيات المستخدمة

- **OpenCV**: لمعالجة الفيديو والصور
- **NumPy**: للعمليات الرياضية
- **Bilateral Filter**: لتنعيم الصورة مع الحفاظ على الحواف
- **Edge Detection**: لاستخراج الحواف
- **K-means Clustering**: لتقليل الألوان
- **Adaptive Thresholding**: لتحسين الحواف

## التثبيت

1. تأكد من وجود Python 3.7 أو أحدث:
```bash
python --version
```

2. قم بتثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

## الاستخدام

### 🖥️ الواجهة الرسومية (مُوصى بها)

#### تشغيل الواجهة الرسومية:
```bash
# Windows
start_gui.bat

# Linux/macOS
./start_gui.sh

# أو مباشرة
python run_gui.py
```

الواجهة الرسومية تتضمن:
- اختيار الملفات بسهولة
- معاينة فورية للنتائج
- شريط تقدم مفصل
- إعدادات متقدمة
- سجل العمليات

📖 **راجع [دليل الواجهة الرسومية](GUI_GUIDE.md) للتفاصيل الكاملة**

### 💻 سطر الأوامر

#### تحويل فيديو كامل
```bash
python main.py input_video.mp4
```

#### تحويل بنمط محدد
```bash
python main.py input_video.mp4 --style smooth
```

#### تحويل مقطع محدد (من الثانية 10 لمدة 30 ثانية)
```bash
python main.py input_video.mp4 --segment 10 30
```

#### تحويل صورة واحدة
```bash
python main.py --image photo.jpg
```

#### تحديد ملف الإخراج
```bash
python main.py input_video.mp4 --output cartoon_video.mp4
```

#### عرض معلومات الفيديو
```bash
python main.py input_video.mp4 --info
```

#### عرض الأنماط المتاحة
```bash
python main.py --list-styles
```

## الأنماط المتاحة

### 1. كلاسيكي (classic)
- النمط الافتراضي
- توازن جيد بين التفاصيل والتبسيط
- مناسب لمعظم أنواع الفيديو

### 2. ناعم (smooth)
- تنعيم أكثر للصورة
- ألوان أكثر تدرجاً
- مناسب للمشاهد الطبيعية

### 3. حاد (sharp)
- حواف أكثر وضوحاً
- ألوان أقل تدرجاً
- مناسب للرسوم المتحركة والشخصيات

## هيكل المشروع

```
├── main.py                 # الملف الرئيسي (سطر الأوامر)
├── gui.py                  # الواجهة الرسومية
├── run_gui.py              # ملف تشغيل الواجهة الرسومية
├── start_gui.bat           # ملف تشغيل Windows
├── start_gui.sh            # ملف تشغيل Linux/macOS
├── video_cartoonizer.py    # فئة تحويل الفيديو
├── image_cartoonizer.py    # فئة تحويل الصور
├── config.py              # إعدادات التطبيق
├── utils.py               # وظائف مساعدة
├── demo.py                # العرض التجريبي
├── test_image.py           # إنشاء صور تجريبية
├── requirements.txt       # متطلبات المشروع
├── README.md             # دليل الاستخدام الرئيسي
├── GUI_GUIDE.md          # دليل الواجهة الرسومية
├── QUICK_START.md        # دليل البدء السريع
├── input/                # مجلد الملفات الأصلية
├── output/               # مجلد النتائج
└── temp/                 # مجلد مؤقت
```

## أمثلة متقدمة

### معالجة متعددة الملفات
```bash
# معالجة جميع ملفات MP4 في مجلد
for file in *.mp4; do
    python main.py "$file" --style classic
done
```

### تحويل بجودة عالية
```bash
python main.py input.mp4 --style sharp --verbose
```

## استكشاف الأخطاء

### 🚨 مشاكل شائعة وحلولها:

#### 1. مشاكل قراءة الملفات
**المشكلة:** "can't open/read file: check file path/integrity"

**الحلول:**
- أعد تسمية الملف بأحرف إنجليزية فقط
- تجنب المسافات والرموز الخاصة في أسماء الملفات
- استخدم الواجهة الرسومية (تحل المشاكل تلقائياً)

#### 2. مشاكل تقنية
```bash
# خطأ في تثبيت OpenCV
pip install opencv-python --upgrade

# نفاد الذاكرة
# استخدم مقاطع أصغر أو قلل دقة الفيديو

# بطء في المعالجة
# استخدم النمط classic للسرعة
```

📖 **راجع [دليل استكشاف الأخطاء](TROUBLESHOOTING.md) للحلول المفصلة**

## المساهمة

نرحب بالمساهمات! يمكنك:
- إضافة أنماط جديدة للكرتون
- تحسين الأداء
- إضافة ميزات جديدة
- تحسين التوثيق

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح issue في المستودع.

---

**ملاحظة**: يتطلب هذا البرنامج موارد حاسوبية جيدة لمعالجة الفيديوهات عالية الدقة. للحصول على أفضل النتائج، استخدم فيديوهات بدقة 1080p أو أقل.
