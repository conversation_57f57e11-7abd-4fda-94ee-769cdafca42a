#!/usr/bin/env python3
"""
برنامج تحويل مقاطع الفيديو إلى كرتون باستخدام الذكاء الاصطناعي
"""

import argparse
import os
import sys
import time
from typing import Optional

from video_cartoonizer import VideoCartoonizer
from image_cartoonizer import ImageCartoonizer
from utils import validate_video_file, get_video_info, create_directories
from config import CARTOON_STYLES, DEFAULT_OUTPUT_DIR

def main():
    """الدالة الرئيسية للبرنامج"""
    parser = argparse.ArgumentParser(
        description="تحويل مقاطع الفيديو إلى كرتون باستخدام الذكاء الاصطناعي",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python main.py input.mp4                          # تحويل فيديو بالنمط الافتراضي
  python main.py input.mp4 --style smooth           # تحويل بنمط ناعم
  python main.py input.mp4 --output cartoon.mp4     # تحديد ملف الإخراج
  python main.py input.mp4 --segment 10 30          # تحويل مقطع من 10 إلى 40 ثانية
  python main.py --image photo.jpg                  # تحويل صورة واحدة
        """
    )
    
    # المعاملات الأساسية
    parser.add_argument('input', nargs='?', help='مسار ملف الفيديو أو الصورة')
    parser.add_argument('--image', help='تحويل صورة واحدة بدلاً من فيديو')
    parser.add_argument('--output', '-o', help='مسار ملف الإخراج')
    parser.add_argument('--output-dir', default=DEFAULT_OUTPUT_DIR, 
                       help=f'مجلد الإخراج (افتراضي: {DEFAULT_OUTPUT_DIR})')
    
    # إعدادات النمط
    parser.add_argument('--style', '-s', choices=list(CARTOON_STYLES.keys()), 
                       default='classic', help='نمط الكرتون (افتراضي: classic)')
    parser.add_argument('--list-styles', action='store_true', 
                       help='عرض الأنماط المتاحة')
    
    # إعدادات المقاطع
    parser.add_argument('--segment', nargs=2, type=float, metavar=('START', 'DURATION'),
                       help='تحويل مقطع محدد (وقت البداية والمدة بالثواني)')
    
    # إعدادات أخرى
    parser.add_argument('--info', action='store_true', 
                       help='عرض معلومات الفيديو فقط')
    parser.add_argument('--verbose', '-v', action='store_true', 
                       help='عرض تفاصيل أكثر')
    
    args = parser.parse_args()
    
    # عرض الأنماط المتاحة
    if args.list_styles:
        print("الأنماط المتاحة:")
        for style, params in CARTOON_STYLES.items():
            print(f"  {style}: {params}")
        return
    
    # التحقق من وجود ملف الإدخال
    input_file = args.input or args.image
    if not input_file:
        parser.error("يجب تحديد ملف الإدخال")
    
    if not os.path.exists(input_file):
        print(f"خطأ: الملف غير موجود: {input_file}")
        sys.exit(1)
    
    try:
        # تحويل صورة واحدة
        if args.image or input_file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
            process_image(args.image or input_file, args)
        else:
            # تحويل فيديو
            if not validate_video_file(input_file):
                print(f"خطأ: صيغة الفيديو غير مدعومة: {input_file}")
                sys.exit(1)
            
            # عرض معلومات الفيديو فقط
            if args.info:
                show_video_info(input_file)
                return
            
            process_video(input_file, args)
            
    except KeyboardInterrupt:
        print("\nتم إيقاف العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"خطأ: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

def process_image(input_path: str, args):
    """معالجة صورة واحدة"""
    print(f"تحويل الصورة: {input_path}")
    
    # إنشاء محول الصور
    cartoonizer = ImageCartoonizer(args.style)
    
    # قراءة الصورة
    import cv2
    image = cv2.imread(input_path)
    if image is None:
        raise ValueError(f"لا يمكن قراءة الصورة: {input_path}")
    
    # تحويل إلى كرتون
    cartoon_image = cartoonizer.cartoonize_image(image)
    
    # تحديد مسار الإخراج
    if args.output:
        output_path = args.output
    else:
        base_name = os.path.splitext(os.path.basename(input_path))[0]
        extension = os.path.splitext(input_path)[1]
        output_path = os.path.join(args.output_dir, f"{base_name}_cartoon_{args.style}{extension}")
    
    # إنشاء مجلد الإخراج
    create_directories(os.path.dirname(output_path))
    
    # حفظ الصورة
    cv2.imwrite(output_path, cartoon_image)
    print(f"تم حفظ الصورة في: {output_path}")

def process_video(input_path: str, args):
    """معالجة فيديو"""
    print(f"تحويل الفيديو: {input_path}")
    
    # إنشاء محول الفيديو
    cartoonizer = VideoCartoonizer(args.style, args.output_dir)
    
    if args.segment:
        # تحويل مقطع محدد
        start_time, duration = args.segment
        output_path = cartoonizer.cartoonize_video_segment(
            input_path, start_time, duration, args.output
        )
    else:
        # تحويل الفيديو كاملاً
        output_path = cartoonizer.cartoonize_video(input_path, args.output)
    
    print(f"تم الانتهاء! الملف محفوظ في: {output_path}")

def show_video_info(input_path: str):
    """عرض معلومات الفيديو"""
    try:
        info = get_video_info(input_path)
        print(f"معلومات الفيديو: {input_path}")
        print(f"  العرض: {info['width']} بكسل")
        print(f"  الارتفاع: {info['height']} بكسل")
        print(f"  معدل الإطارات: {info['fps']:.2f} إطار/ثانية")
        print(f"  عدد الإطارات: {info['frame_count']}")
        print(f"  المدة: {info['duration']:.2f} ثانية")
    except Exception as e:
        print(f"خطأ في قراءة معلومات الفيديو: {e}")

if __name__ == "__main__":
    main()
