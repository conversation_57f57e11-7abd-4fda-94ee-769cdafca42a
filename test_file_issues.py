#!/usr/bin/env python3
"""
اختبار حل مشاكل الملفات والمسارات
"""

import os
import cv2
import numpy as np
from PIL import Image
import tempfile
import shutil
from file_helper import FileHelper

def create_test_files():
    """إنشاء ملفات اختبار بأسماء مختلفة"""
    print("إنشاء ملفات اختبار...")
    
    # إنشاء مجلد اختبار
    test_dir = "test_files"
    os.makedirs(test_dir, exist_ok=True)
    
    # إنشاء صورة تجريبية
    test_image = np.zeros((300, 400, 3), dtype=np.uint8)
    test_image[:, :] = [100, 150, 200]  # لون أزرق فاتح
    
    # رسم بعض الأشكال
    cv2.circle(test_image, (200, 150), 50, (255, 255, 255), -1)
    cv2.rectangle(test_image, (50, 50), (150, 100), (0, 255, 0), -1)
    cv2.putText(test_image, 'Test', (160, 200), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    # حفظ بأسماء مختلفة
    test_files = [
        "normal_image.jpg",                    # اسم عادي
        "image with spaces.jpg",               # مسافات
        "صورة_عربية.jpg",                     # أحرف عربية
        "image-2025-05-28_005704.jpg",        # رموز خاصة
        "تجربة صورة 123.png",                 # مزيج عربي وإنجليزي
        "image@#$%^&().jpg"                   # رموز خاصة كثيرة
    ]
    
    created_files = []
    for filename in test_files:
        filepath = os.path.join(test_dir, filename)
        try:
            cv2.imwrite(filepath, test_image)
            if os.path.exists(filepath):
                created_files.append(filepath)
                print(f"✓ تم إنشاء: {filename}")
            else:
                print(f"✗ فشل في إنشاء: {filename}")
        except Exception as e:
            print(f"✗ خطأ في إنشاء {filename}: {e}")
    
    return created_files

def test_opencv_reading(files):
    """اختبار قراءة الملفات بـ OpenCV العادي"""
    print("\n" + "="*50)
    print("اختبار قراءة OpenCV العادي")
    print("="*50)
    
    results = {}
    for filepath in files:
        filename = os.path.basename(filepath)
        try:
            image = cv2.imread(filepath)
            if image is not None:
                print(f"✓ نجح: {filename}")
                results[filepath] = True
            else:
                print(f"✗ فشل: {filename}")
                results[filepath] = False
        except Exception as e:
            print(f"✗ خطأ في {filename}: {e}")
            results[filepath] = False
    
    return results

def test_filehelper_reading(files):
    """اختبار قراءة الملفات بـ FileHelper"""
    print("\n" + "="*50)
    print("اختبار قراءة FileHelper")
    print("="*50)
    
    results = {}
    for filepath in files:
        filename = os.path.basename(filepath)
        try:
            image = FileHelper.safe_read_image(filepath)
            if image is not None:
                print(f"✓ نجح: {filename}")
                results[filepath] = True
            else:
                print(f"✗ فشل: {filename}")
                results[filepath] = False
        except Exception as e:
            print(f"✗ خطأ في {filename}: {e}")
            results[filepath] = False
    
    return results

def test_filename_sanitization(files):
    """اختبار تنظيف أسماء الملفات"""
    print("\n" + "="*50)
    print("اختبار تنظيف أسماء الملفات")
    print("="*50)
    
    for filepath in files:
        original_name = os.path.basename(filepath)
        safe_name = FileHelper.sanitize_filename(original_name)
        print(f"الأصلي: {original_name}")
        print(f"المنظف: {safe_name}")
        print("-" * 30)

def test_temp_copy_method(files):
    """اختبار طريقة النسخ إلى مجلد مؤقت"""
    print("\n" + "="*50)
    print("اختبار النسخ إلى مجلد مؤقت")
    print("="*50)
    
    results = {}
    for filepath in files:
        filename = os.path.basename(filepath)
        try:
            temp_path = FileHelper.copy_to_temp(filepath)
            if temp_path and os.path.exists(temp_path):
                # محاولة قراءة الملف المؤقت
                image = cv2.imread(temp_path)
                if image is not None:
                    print(f"✓ نجح النسخ والقراءة: {filename}")
                    results[filepath] = True
                    
                    # تنظيف الملف المؤقت
                    try:
                        os.remove(temp_path)
                        os.rmdir(os.path.dirname(temp_path))
                    except:
                        pass
                else:
                    print(f"✗ نجح النسخ لكن فشلت القراءة: {filename}")
                    results[filepath] = False
            else:
                print(f"✗ فشل النسخ: {filename}")
                results[filepath] = False
        except Exception as e:
            print(f"✗ خطأ في {filename}: {e}")
            results[filepath] = False
    
    return results

def test_safe_writing(files):
    """اختبار الكتابة الآمنة"""
    print("\n" + "="*50)
    print("اختبار الكتابة الآمنة")
    print("="*50)
    
    # إنشاء مجلد اختبار للإخراج
    output_dir = "test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    for filepath in files:
        filename = os.path.basename(filepath)
        try:
            # قراءة الصورة
            image = FileHelper.safe_read_image(filepath)
            if image is not None:
                # تحديد مسار الإخراج
                safe_name = FileHelper.sanitize_filename(filename)
                output_path = os.path.join(output_dir, f"output_{safe_name}")
                
                # محاولة الكتابة
                success = FileHelper.safe_write_image(output_path, image)
                if success:
                    print(f"✓ نجحت الكتابة: {filename} -> {safe_name}")
                else:
                    print(f"✗ فشلت الكتابة: {filename}")
            else:
                print(f"✗ لا يمكن قراءة الملف للكتابة: {filename}")
        except Exception as e:
            print(f"✗ خطأ في كتابة {filename}: {e}")

def compare_results(opencv_results, filehelper_results):
    """مقارنة نتائج الطرق المختلفة"""
    print("\n" + "="*50)
    print("مقارنة النتائج")
    print("="*50)
    
    print(f"{'الملف':<30} {'OpenCV':<10} {'FileHelper':<12} {'التحسن'}")
    print("-" * 65)
    
    improvements = 0
    total_files = len(opencv_results)
    
    for filepath in opencv_results:
        filename = os.path.basename(filepath)
        opencv_ok = opencv_results[filepath]
        filehelper_ok = filehelper_results.get(filepath, False)
        
        improved = "✓" if (not opencv_ok and filehelper_ok) else ""
        if improved:
            improvements += 1
        
        print(f"{filename[:28]:<30} {'✓' if opencv_ok else '✗':<10} {'✓' if filehelper_ok else '✗':<12} {improved}")
    
    print("-" * 65)
    print(f"إجمالي الملفات: {total_files}")
    print(f"نجح OpenCV: {sum(opencv_results.values())}")
    print(f"نجح FileHelper: {sum(filehelper_results.values())}")
    print(f"التحسينات: {improvements}")

def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    print("\nتنظيف ملفات الاختبار...")
    
    for directory in ["test_files", "test_output"]:
        if os.path.exists(directory):
            try:
                shutil.rmtree(directory)
                print(f"✓ تم حذف مجلد: {directory}")
            except Exception as e:
                print(f"✗ خطأ في حذف {directory}: {e}")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار حل مشاكل الملفات والمسارات")
    print("="*60)
    
    try:
        # إنشاء ملفات الاختبار
        test_files = create_test_files()
        
        if not test_files:
            print("❌ لم يتم إنشاء أي ملفات اختبار")
            return
        
        # اختبار الطرق المختلفة
        opencv_results = test_opencv_reading(test_files)
        filehelper_results = test_filehelper_reading(test_files)
        
        # اختبارات إضافية
        test_filename_sanitization(test_files)
        test_temp_copy_method(test_files)
        test_safe_writing(test_files)
        
        # مقارنة النتائج
        compare_results(opencv_results, filehelper_results)
        
        print("\n🎉 انتهى الاختبار!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
    
    finally:
        # تنظيف الملفات
        cleanup_test_files()

if __name__ == "__main__":
    main()
