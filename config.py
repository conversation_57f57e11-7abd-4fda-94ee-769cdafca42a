"""
إعدادات التطبيق لتحويل الفيديو إلى كرتون
"""

# إعدادات الفيديو
DEFAULT_OUTPUT_FORMAT = 'mp4'
DEFAULT_FPS = 30
DEFAULT_QUALITY = 95

# إعدادات تحويل الكرتون
CARTOON_STYLES = {
    # الأنماط الأساسية
    'classic': {
        'bilateral_d': 9,
        'bilateral_sigma_color': 200,
        'bilateral_sigma_space': 200,
        'median_blur_value': 7,
        'adaptive_threshold_block_size': 9,
        'adaptive_threshold_c': 10,
        'num_colors': 8,
        'edge_thickness': 2,
        'color_saturation': 1.2,
        'brightness_boost': 1.1
    },
    'smooth': {
        'bilateral_d': 15,
        'bilateral_sigma_color': 300,
        'bilateral_sigma_space': 300,
        'median_blur_value': 11,
        'adaptive_threshold_block_size': 7,
        'adaptive_threshold_c': 8,
        'num_colors': 12,
        'edge_thickness': 1,
        'color_saturation': 1.0,
        'brightness_boost': 1.0
    },
    'sharp': {
        'bilateral_d': 5,
        'bilateral_sigma_color': 100,
        'bilateral_sigma_space': 100,
        'median_blur_value': 3,
        'adaptive_threshold_block_size': 11,
        'adaptive_threshold_c': 12,
        'num_colors': 6,
        'edge_thickness': 3,
        'color_saturation': 1.4,
        'brightness_boost': 1.2
    },

    # الأنماط الجديدة المتقدمة
    'anime': {
        'bilateral_d': 12,
        'bilateral_sigma_color': 250,
        'bilateral_sigma_space': 250,
        'median_blur_value': 9,
        'adaptive_threshold_block_size': 9,
        'adaptive_threshold_c': 8,
        'num_colors': 6,
        'edge_thickness': 4,
        'color_saturation': 1.6,
        'brightness_boost': 1.3,
        'contrast_boost': 1.2,
        'use_color_enhancement': True,
        'use_skin_detection': True
    },
    'disney': {
        'bilateral_d': 18,
        'bilateral_sigma_color': 350,
        'bilateral_sigma_space': 350,
        'median_blur_value': 13,
        'adaptive_threshold_block_size': 7,
        'adaptive_threshold_c': 6,
        'num_colors': 10,
        'edge_thickness': 2,
        'color_saturation': 1.4,
        'brightness_boost': 1.25,
        'contrast_boost': 1.1,
        'use_color_enhancement': True,
        'use_gradient_smoothing': True
    },
    'pixar': {
        'bilateral_d': 14,
        'bilateral_sigma_color': 280,
        'bilateral_sigma_space': 280,
        'median_blur_value': 11,
        'adaptive_threshold_block_size': 9,
        'adaptive_threshold_c': 7,
        'num_colors': 8,
        'edge_thickness': 2,
        'color_saturation': 1.3,
        'brightness_boost': 1.2,
        'contrast_boost': 1.15,
        'use_color_enhancement': True,
        'use_shadow_enhancement': True
    },
    'comic': {
        'bilateral_d': 8,
        'bilateral_sigma_color': 180,
        'bilateral_sigma_space': 180,
        'median_blur_value': 5,
        'adaptive_threshold_block_size': 11,
        'adaptive_threshold_c': 15,
        'num_colors': 5,
        'edge_thickness': 5,
        'color_saturation': 1.8,
        'brightness_boost': 1.4,
        'contrast_boost': 1.3,
        'use_color_enhancement': True,
        'use_halftone_effect': True
    },
    'watercolor': {
        'bilateral_d': 20,
        'bilateral_sigma_color': 400,
        'bilateral_sigma_space': 400,
        'median_blur_value': 15,
        'adaptive_threshold_block_size': 5,
        'adaptive_threshold_c': 4,
        'num_colors': 15,
        'edge_thickness': 1,
        'color_saturation': 0.9,
        'brightness_boost': 1.1,
        'contrast_boost': 0.9,
        'use_texture_effect': True,
        'use_soft_edges': True
    },
    'oil_painting': {
        'bilateral_d': 16,
        'bilateral_sigma_color': 320,
        'bilateral_sigma_space': 320,
        'median_blur_value': 11,  # تأكد من أن القيمة فردية
        'adaptive_threshold_block_size': 7,
        'adaptive_threshold_c': 5,
        'num_colors': 12,
        'edge_thickness': 2,
        'color_saturation': 1.2,
        'brightness_boost': 1.15,
        'contrast_boost': 1.1,
        'use_texture_effect': True,
        'use_brush_strokes': True
    },
    'vintage_cartoon': {
        'bilateral_d': 10,
        'bilateral_sigma_color': 220,
        'bilateral_sigma_space': 220,
        'median_blur_value': 7,  # تأكد من أن القيمة فردية
        'adaptive_threshold_block_size': 9,
        'adaptive_threshold_c': 12,
        'num_colors': 7,
        'edge_thickness': 3,
        'color_saturation': 0.8,
        'brightness_boost': 0.95,
        'contrast_boost': 1.2,
        'use_sepia_tone': True,
        'use_film_grain': True
    },
    'neon': {
        'bilateral_d': 6,
        'bilateral_sigma_color': 150,
        'bilateral_sigma_space': 150,
        'median_blur_value': 5,  # تأكد من أن القيمة فردية
        'adaptive_threshold_block_size': 13,
        'adaptive_threshold_c': 18,
        'num_colors': 4,
        'edge_thickness': 6,
        'color_saturation': 2.0,
        'brightness_boost': 1.5,
        'contrast_boost': 1.4,
        'use_glow_effect': True,
        'use_high_contrast': True
    }
}

# مسارات الملفات
DEFAULT_INPUT_DIR = 'input'
DEFAULT_OUTPUT_DIR = 'output'
TEMP_DIR = 'temp'

# وصف الأنماط
STYLE_DESCRIPTIONS = {
    'classic': 'متوازن - مناسب لمعظم الاستخدامات',
    'smooth': 'ناعم - مناسب للمناظر الطبيعية',
    'sharp': 'حاد - مناسب للشخصيات والرسوم',
    'anime': 'أنمي - نمط الرسوم اليابانية مع ألوان زاهية',
    'disney': 'ديزني - نمط أفلام ديزني الكلاسيكية',
    'pixar': 'بيكسار - نمط الرسوم ثلاثية الأبعاد',
    'comic': 'كوميك - نمط الكتب المصورة مع حواف سميكة',
    'watercolor': 'ألوان مائية - نمط الرسم بالألوان المائية',
    'oil_painting': 'رسم زيتي - نمط اللوحات الزيتية',
    'vintage_cartoon': 'كرتون قديم - نمط الكرتون الكلاسيكي',
    'neon': 'نيون - نمط مضيء بألوان فلورية'
}

# إعدادات المعالجة
MAX_FRAME_WIDTH = 1920
MAX_FRAME_HEIGHT = 1080
BATCH_SIZE = 10
