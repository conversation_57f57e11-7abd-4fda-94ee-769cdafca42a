#!/usr/bin/env python3
"""
ملف تشغيل الواجهة الرسومية لبرنامج تحويل الفيديو إلى كرتون
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gui import main
    
    if __name__ == "__main__":
        print("🎬 تشغيل واجهة محول الفيديو إلى كرتون...")
        main()
        
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("يرجى التأكد من تثبيت جميع المتطلبات:")
    print("pip install opencv-python numpy matplotlib Pillow tqdm")
    input("اضغط Enter للخروج...")
    
except Exception as e:
    print(f"خطأ في تشغيل التطبيق: {e}")
    input("اضغط Enter للخروج...")
