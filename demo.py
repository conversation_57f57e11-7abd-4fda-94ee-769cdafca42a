#!/usr/bin/env python3
"""
ملف تجريبي لعرض إمكانيات برنامج تحويل الفيديو إلى كرتون
"""

import cv2
import numpy as np
import os
import time
from image_cartoonizer import ImageCartoonizer
from video_cartoonizer import VideoCartoonizer

def create_demo_video():
    """إنشاء فيديو تجريبي قصير للاختبار"""
    print("إنشاء فيديو تجريبي...")
    
    # إعدادات الفيديو
    width, height = 640, 480
    fps = 30
    duration = 3  # 3 ثوان
    total_frames = fps * duration
    
    # إنشاء كاتب الفيديو
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    output_path = 'input/demo_video.mp4'
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    for frame_num in range(total_frames):
        # إنشاء إطار ملون متحرك
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # خلفية متدرجة متحركة
        for i in range(height):
            color_shift = (frame_num * 2) % 255
            frame[i, :] = [(100 + color_shift) % 255, (150 + i//3) % 255, (200 - i//4) % 255]
        
        # دائرة متحركة
        center_x = int(width/2 + 100 * np.sin(frame_num * 0.1))
        center_y = int(height/2 + 50 * np.cos(frame_num * 0.1))
        cv2.circle(frame, (center_x, center_y), 50, (0, 255, 255), -1)
        
        # مستطيل دوار
        angle = frame_num * 3
        rect_center = (width//2, height//2)
        rect_size = (80, 40)
        
        # حساب نقاط المستطيل الدوار
        cos_a = np.cos(np.radians(angle))
        sin_a = np.sin(np.radians(angle))
        
        points = []
        for dx, dy in [(-40, -20), (40, -20), (40, 20), (-40, 20)]:
            x = int(rect_center[0] + dx * cos_a - dy * sin_a)
            y = int(rect_center[1] + dx * sin_a + dy * cos_a)
            points.append([x, y])
        
        points = np.array(points, np.int32)
        cv2.fillPoly(frame, [points], (255, 0, 0))
        
        # نص متحرك
        text_x = (frame_num * 5) % (width + 200) - 100
        cv2.putText(frame, f'Frame {frame_num}', (text_x, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print(f"تم إنشاء الفيديو التجريبي: {output_path}")
    return output_path

def demo_image_cartoonization():
    """عرض تحويل الصور إلى كرتون"""
    print("\n=== تجربة تحويل الصور إلى كرتون ===")
    
    # إنشاء صورة تجريبية معقدة
    image = np.zeros((400, 600, 3), dtype=np.uint8)
    
    # خلفية متدرجة
    for i in range(400):
        for j in range(600):
            image[i, j] = [
                int(255 * (i/400)),
                int(255 * (j/600)),
                int(255 * ((i+j)/(400+600)))
            ]
    
    # إضافة أشكال مختلفة
    cv2.circle(image, (150, 150), 60, (255, 0, 0), -1)
    cv2.rectangle(image, (300, 100), (500, 200), (0, 255, 0), -1)
    cv2.ellipse(image, (400, 300), (80, 40), 45, 0, 360, (0, 0, 255), -1)
    
    # حفظ الصورة الأصلية
    original_path = 'input/complex_image.jpg'
    cv2.imwrite(original_path, image)
    
    # تجربة جميع الأنماط
    cartoonizer = ImageCartoonizer()
    
    for style in ['classic', 'smooth', 'sharp']:
        print(f"تحويل بالنمط: {style}")
        cartoonizer.set_style(style)
        
        start_time = time.time()
        cartoon_image = cartoonizer.cartoonize_image(image)
        processing_time = time.time() - start_time
        
        output_path = f'output/complex_image_cartoon_{style}.jpg'
        cv2.imwrite(output_path, cartoon_image)
        
        print(f"  تم الحفظ في: {output_path}")
        print(f"  وقت المعالجة: {processing_time:.2f} ثانية")

def demo_video_cartoonization():
    """عرض تحويل الفيديو إلى كرتون"""
    print("\n=== تجربة تحويل الفيديو إلى كرتون ===")
    
    # إنشاء فيديو تجريبي
    video_path = create_demo_video()
    
    # تحويل الفيديو بأنماط مختلفة
    cartoonizer = VideoCartoonizer()
    
    for style in ['classic', 'smooth']:  # نمطين فقط لتوفير الوقت
        print(f"\nتحويل الفيديو بالنمط: {style}")
        cartoonizer.set_style(style)
        
        start_time = time.time()
        output_path = cartoonizer.cartoonize_video(video_path)
        processing_time = time.time() - start_time
        
        print(f"تم الحفظ في: {output_path}")
        print(f"وقت المعالجة: {processing_time:.2f} ثانية")

def demo_video_segment():
    """عرض تحويل مقطع من الفيديو"""
    print("\n=== تجربة تحويل مقطع من الفيديو ===")
    
    video_path = 'input/demo_video.mp4'
    if not os.path.exists(video_path):
        video_path = create_demo_video()
    
    cartoonizer = VideoCartoonizer('sharp')
    
    # تحويل مقطع من الثانية 1 لمدة ثانية واحدة
    print("تحويل مقطع من الثانية 1 لمدة ثانية واحدة...")
    start_time = time.time()
    output_path = cartoonizer.cartoonize_video_segment(video_path, 1.0, 1.0)
    processing_time = time.time() - start_time
    
    print(f"تم الحفظ في: {output_path}")
    print(f"وقت المعالجة: {processing_time:.2f} ثانية")

def main():
    """الدالة الرئيسية للعرض التجريبي"""
    print("🎬 مرحباً بك في العرض التجريبي لبرنامج تحويل الفيديو إلى كرتون!")
    print("=" * 60)
    
    # إنشاء المجلدات المطلوبة
    os.makedirs('input', exist_ok=True)
    os.makedirs('output', exist_ok=True)
    
    try:
        # تجربة تحويل الصور
        demo_image_cartoonization()
        
        # تجربة تحويل الفيديو
        demo_video_cartoonization()
        
        # تجربة تحويل مقطع
        demo_video_segment()
        
        print("\n" + "=" * 60)
        print("🎉 تم الانتهاء من العرض التجريبي بنجاح!")
        print("يمكنك الآن فحص الملفات في مجلد 'output' لرؤية النتائج.")
        
    except Exception as e:
        print(f"❌ حدث خطأ أثناء العرض التجريبي: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
