"""
فئة لتحويل مقاطع الفيديو إلى كرتون
"""

import cv2
import numpy as np
import os
import time
from typing import Optional, Callable
from tqdm import tqdm

from image_cartoonizer import ImageCartoonizer
from utils import (
    get_video_info, resize_frame, get_output_filename,
    calculate_processing_time, print_progress, create_directories
)
from config import DEFAULT_OUTPUT_DIR, TEMP_DIR, MAX_FRAME_WIDTH, MAX_FRAME_HEIGHT

class VideoCartoonizer:
    """فئة لتحويل مقاطع الفيديو إلى كرتون"""
    
    def __init__(self, style: str = 'classic', output_dir: str = DEFAULT_OUTPUT_DIR):
        """
        تهيئة محول الفيديو إلى كرتون
        
        Args:
            style: نمط الكرتون
            output_dir: مجلد الإخراج
        """
        self.image_cartoonizer = ImageCartoonizer(style)
        self.output_dir = output_dir
        self.style = style
        
        # إنشاء المجلدات المطلوبة
        create_directories(self.output_dir, TEMP_DIR)
    
    def cartoonize_video(self, 
                        input_path: str, 
                        output_path: Optional[str] = None,
                        progress_callback: Optional[Callable] = None) -> str:
        """
        تحويل فيديو كامل إلى كرتون
        
        Args:
            input_path: مسار الفيديو الأصلي
            output_path: مسار الفيديو الناتج (اختياري)
            progress_callback: دالة لتتبع التقدم (اختياري)
            
        Returns:
            مسار الفيديو الناتج
        """
        print(f"بدء تحويل الفيديو: {input_path}")
        
        # التحقق من وجود الملف
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"الملف غير موجود: {input_path}")
        
        # الحصول على معلومات الفيديو
        video_info = get_video_info(input_path)
        print(f"معلومات الفيديو: {video_info}")
        
        # تحديد مسار الإخراج
        if output_path is None:
            output_path = get_output_filename(input_path, self.output_dir, self.style)
        
        # فتح الفيديو الأصلي
        cap = cv2.VideoCapture(input_path)
        
        # إعداد كاتب الفيديو
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        
        # قراءة الإطار الأول لتحديد الأبعاد
        ret, first_frame = cap.read()
        if not ret:
            raise ValueError("لا يمكن قراءة الفيديو")
        
        # تغيير حجم الإطار إذا لزم الأمر
        first_frame = resize_frame(first_frame, MAX_FRAME_WIDTH, MAX_FRAME_HEIGHT)
        height, width = first_frame.shape[:2]
        
        # إنشاء كاتب الفيديو
        out = cv2.VideoWriter(output_path, fourcc, video_info['fps'], (width, height))
        
        # إعادة تعيين موضع الفيديو
        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        # متغيرات التتبع
        frame_count = 0
        total_frames = int(video_info['frame_count'])
        start_time = time.time()
        
        print(f"بدء معالجة {total_frames} إطار...")
        
        # معالجة كل إطار
        with tqdm(total=total_frames, desc="معالجة الإطارات", unit="إطار") as pbar:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # تغيير حجم الإطار
                frame = resize_frame(frame, MAX_FRAME_WIDTH, MAX_FRAME_HEIGHT)
                
                # تحويل الإطار إلى كرتون
                cartoon_frame = self.image_cartoonizer.cartoonize_image(frame)
                
                # كتابة الإطار
                out.write(cartoon_frame)
                
                frame_count += 1
                pbar.update(1)
                
                # استدعاء دالة التقدم إذا كانت متوفرة
                if progress_callback:
                    progress_callback(frame_count, total_frames)
                
                # طباعة التقدم كل 30 إطار
                if frame_count % 30 == 0:
                    elapsed, remaining = calculate_processing_time(start_time, frame_count, total_frames)
                    print(f"\nالإطار {frame_count}/{total_frames} - "
                          f"الوقت المنقضي: {elapsed:.1f}ث - "
                          f"الوقت المتبقي: {remaining:.1f}ث")
        
        # تنظيف الموارد
        cap.release()
        out.release()
        
        total_time = time.time() - start_time
        print(f"\nتم الانتهاء من التحويل!")
        print(f"الوقت الإجمالي: {total_time:.2f} ثانية")
        print(f"الملف المحفوظ في: {output_path}")
        
        return output_path
    
    def cartoonize_video_segment(self, 
                               input_path: str, 
                               start_time: float, 
                               duration: float,
                               output_path: Optional[str] = None) -> str:
        """
        تحويل جزء من الفيديو إلى كرتون
        
        Args:
            input_path: مسار الفيديو الأصلي
            start_time: وقت البداية بالثواني
            duration: مدة المقطع بالثواني
            output_path: مسار الإخراج (اختياري)
            
        Returns:
            مسار الفيديو الناتج
        """
        print(f"تحويل مقطع من الفيديو: {start_time}ث إلى {start_time + duration}ث")
        
        # فتح الفيديو
        cap = cv2.VideoCapture(input_path)
        video_info = get_video_info(input_path)
        
        # تحديد الإطارات
        start_frame = int(start_time * video_info['fps'])
        end_frame = int((start_time + duration) * video_info['fps'])
        
        # تحديد مسار الإخراج
        if output_path is None:
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            output_path = os.path.join(
                self.output_dir, 
                f"{base_name}_segment_{start_time}_{duration}_{self.style}.mp4"
            )
        
        # الانتقال إلى الإطار المطلوب
        cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
        
        # قراءة الإطار الأول لتحديد الأبعاد
        ret, first_frame = cap.read()
        if not ret:
            raise ValueError("لا يمكن قراءة الفيديو")
        
        first_frame = resize_frame(first_frame, MAX_FRAME_WIDTH, MAX_FRAME_HEIGHT)
        height, width = first_frame.shape[:2]
        
        # إنشاء كاتب الفيديو
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, video_info['fps'], (width, height))
        
        # إعادة تعيين الموضع
        cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
        
        frame_count = 0
        total_frames = end_frame - start_frame
        
        print(f"معالجة {total_frames} إطار...")
        
        while frame_count < total_frames:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame = resize_frame(frame, MAX_FRAME_WIDTH, MAX_FRAME_HEIGHT)
            cartoon_frame = self.image_cartoonizer.cartoonize_image(frame)
            out.write(cartoon_frame)
            
            frame_count += 1
            print_progress(frame_count, total_frames, "معالجة المقطع")
        
        cap.release()
        out.release()
        
        print(f"\nتم حفظ المقطع في: {output_path}")
        return output_path
    
    def set_style(self, style: str):
        """تغيير نمط الكرتون"""
        self.style = style
        self.image_cartoonizer.set_style(style)
    
    def get_available_styles(self) -> list:
        """الحصول على الأنماط المتاحة"""
        return self.image_cartoonizer.get_available_styles()
