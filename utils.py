"""
وظائف مساعدة لمعالجة الفيديو والصور
"""

import os
import cv2
import numpy as np
from typing import Tuple, Optional

def create_directories(*dirs):
    """إنشاء المجلدات إذا لم تكن موجودة"""
    for directory in dirs:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"تم إنشاء المجلد: {directory}")

def get_video_info(video_path: str) -> dict:
    """الحصول على معلومات الفيديو"""
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        raise ValueError(f"لا يمكن فتح الفيديو: {video_path}")
    
    info = {
        'fps': cap.get(cv2.CAP_PROP_FPS),
        'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
        'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
        'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
        'duration': cap.get(cv2.CAP_PROP_FRAME_COUNT) / cap.get(cv2.CAP_PROP_FPS)
    }
    
    cap.release()
    return info

def resize_frame(frame: np.ndarray, max_width: int = 1920, max_height: int = 1080) -> np.ndarray:
    """تغيير حجم الإطار مع الحفاظ على النسبة"""
    height, width = frame.shape[:2]
    
    # حساب النسبة الجديدة
    scale = min(max_width / width, max_height / height)
    
    if scale < 1:
        new_width = int(width * scale)
        new_height = int(height * scale)
        frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
    
    return frame

def validate_video_file(file_path: str) -> bool:
    """التحقق من صحة ملف الفيديو"""
    if not os.path.exists(file_path):
        return False
    
    # قائمة بصيغ الفيديو المدعومة
    supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    file_extension = os.path.splitext(file_path)[1].lower()
    
    return file_extension in supported_formats

def get_output_filename(input_path: str, output_dir: str, style: str, extension: str = 'mp4') -> str:
    """إنشاء اسم ملف الإخراج"""
    base_name = os.path.splitext(os.path.basename(input_path))[0]
    output_filename = f"{base_name}_cartoon_{style}.{extension}"
    return os.path.join(output_dir, output_filename)

def calculate_processing_time(start_time: float, current_frame: int, total_frames: int) -> Tuple[float, float]:
    """حساب الوقت المتبقي للمعالجة"""
    import time
    
    elapsed_time = time.time() - start_time
    if current_frame > 0:
        time_per_frame = elapsed_time / current_frame
        remaining_time = time_per_frame * (total_frames - current_frame)
        return elapsed_time, remaining_time
    return elapsed_time, 0

def print_progress(current: int, total: int, prefix: str = "التقدم", bar_length: int = 50):
    """طباعة شريط التقدم"""
    percent = current / total
    filled_length = int(bar_length * percent)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)
    print(f'\r{prefix}: |{bar}| {percent:.1%} ({current}/{total})', end='', flush=True)
    
    if current == total:
        print()  # سطر جديد عند الانتهاء
