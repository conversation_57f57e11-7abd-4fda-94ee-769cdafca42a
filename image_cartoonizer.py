"""
فئة لتحويل الصور إلى كرتون باستخدام OpenCV
"""

import cv2
import numpy as np
from typing import Dict, Any
from config import CARTOON_STYLES

class ImageCartoonizer:
    """فئة لتحويل الصور إلى كرتون"""
    
    def __init__(self, style: str = 'classic'):
        """
        تهيئة محول الصور إلى كرتون
        
        Args:
            style: نمط الكرتون ('classic', 'smooth', 'sharp')
        """
        if style not in CARTOON_STYLES:
            raise ValueError(f"النمط غير مدعوم: {style}. الأنماط المدعومة: {list(CARTOON_STYLES.keys())}")
        
        self.style = style
        self.params = CARTOON_STYLES[style]
    
    def cartoonize_image(self, image: np.ndarray) -> np.ndarray:
        """
        تحويل صورة واحدة إلى كرتون
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة المحولة إلى كرتون
        """
        # الخطوة 1: تطبيق Bilateral Filter لتنعيم الصورة مع الحفاظ على الحواف
        smooth = cv2.bilateralFilter(
            image, 
            self.params['bilateral_d'],
            self.params['bilateral_sigma_color'],
            self.params['bilateral_sigma_space']
        )
        
        # الخطوة 2: تحويل إلى رمادي لاستخراج الحواف
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray_blur = cv2.medianBlur(gray, self.params['median_blur_value'])
        
        # الخطوة 3: استخراج الحواف باستخدام Adaptive Threshold
        edges = cv2.adaptiveThreshold(
            gray_blur, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY,
            self.params['adaptive_threshold_block_size'],
            self.params['adaptive_threshold_c']
        )
        
        # الخطوة 4: تحويل الحواف إلى 3 قنوات
        edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
        
        # الخطوة 5: تقليل عدد الألوان باستخدام K-means
        cartoon = self._reduce_colors(smooth, self.params['num_colors'])
        
        # الخطوة 6: دمج الحواف مع الصورة المنعمة
        cartoon = cv2.bitwise_and(cartoon, edges)
        
        return cartoon
    
    def _reduce_colors(self, image: np.ndarray, k: int) -> np.ndarray:
        """
        تقليل عدد الألوان في الصورة باستخدام K-means clustering
        
        Args:
            image: الصورة الأصلية
            k: عدد الألوان المطلوب
            
        Returns:
            الصورة مع عدد ألوان مقلل
        """
        # تحويل الصورة إلى مصفوفة ثنائية الأبعاد
        data = image.reshape((-1, 3))
        data = np.float32(data)
        
        # تطبيق K-means
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
        _, labels, centers = cv2.kmeans(data, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
        
        # تحويل النتائج إلى uint8
        centers = np.uint8(centers)
        
        # إعادة بناء الصورة
        segmented_data = centers[labels.flatten()]
        segmented_image = segmented_data.reshape(image.shape)
        
        return segmented_image
    
    def apply_edge_enhancement(self, image: np.ndarray) -> np.ndarray:
        """
        تحسين الحواف في الصورة
        
        Args:
            image: الصورة الأصلية
            
        Returns:
            الصورة مع حواف محسنة
        """
        # تحويل إلى رمادي
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # تطبيق Gaussian blur
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # استخراج الحواف باستخدام Canny
        edges = cv2.Canny(blurred, 50, 150)
        
        # تحويل إلى 3 قنوات
        edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
        
        # دمج مع الصورة الأصلية
        result = cv2.addWeighted(image, 0.8, edges, 0.2, 0)
        
        return result
    
    def apply_color_quantization(self, image: np.ndarray, levels: int = 8) -> np.ndarray:
        """
        تطبيق تكميم الألوان
        
        Args:
            image: الصورة الأصلية
            levels: عدد مستويات الألوان
            
        Returns:
            الصورة مع ألوان مكممة
        """
        # تقسيم القيم إلى مستويات
        factor = 255 // (levels - 1)
        quantized = (image // factor) * factor
        
        return quantized.astype(np.uint8)
    
    def get_available_styles(self) -> list:
        """الحصول على قائمة الأنماط المتاحة"""
        return list(CARTOON_STYLES.keys())
    
    def set_style(self, style: str):
        """تغيير نمط الكرتون"""
        if style not in CARTOON_STYLES:
            raise ValueError(f"النمط غير مدعوم: {style}")
        
        self.style = style
        self.params = CARTOON_STYLES[style]
