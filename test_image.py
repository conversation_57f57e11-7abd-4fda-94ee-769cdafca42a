#!/usr/bin/env python3
"""
ملف اختبار لإنشاء صورة تجريبية واختبار تحويلها إلى كرتون
"""

import cv2
import numpy as np
import os

def create_test_image():
    """إنشاء صورة تجريبية للاختبار"""
    # إنشاء صورة ملونة بسيطة
    height, width = 400, 600
    image = np.zeros((height, width, 3), dtype=np.uint8)
    
    # خلفية زرقاء متدرجة
    for i in range(height):
        image[i, :] = [255 - i//2, 100 + i//3, 200]
    
    # رسم دائرة حمراء
    cv2.circle(image, (150, 150), 80, (0, 0, 255), -1)
    
    # رسم مستطيل أخضر
    cv2.rectangle(image, (300, 100), (500, 250), (0, 255, 0), -1)
    
    # رسم مثلث أصفر
    points = np.array([[400, 300], [350, 380], [450, 380]], np.int32)
    cv2.fillPoly(image, [points], (0, 255, 255))
    
    # إضافة نص
    cv2.putText(image, 'Test Image', (200, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    return image

def main():
    """الدالة الرئيسية"""
    # إنشاء مجلد الإدخال إذا لم يكن موجوداً
    os.makedirs('input', exist_ok=True)
    
    # إنشاء الصورة التجريبية
    test_image = create_test_image()
    
    # حفظ الصورة
    input_path = 'input/test_image.jpg'
    cv2.imwrite(input_path, test_image)
    print(f"تم إنشاء الصورة التجريبية: {input_path}")
    
    # عرض الصورة (اختياري)
    cv2.imshow('Test Image', test_image)
    cv2.waitKey(3000)  # انتظار 3 ثوان
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
