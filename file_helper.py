#!/usr/bin/env python3
"""
أداة مساعدة لحل مشاكل مسارات الملفات والرموز الخاصة
"""

import os
import cv2
import numpy as np
import re
import shutil
from PIL import Image
import tempfile

class FileHelper:
    """فئة مساعدة للتعامل مع الملفات والمسارات"""
    
    @staticmethod
    def is_valid_path(file_path):
        """التحقق من صحة مسار الملف"""
        try:
            return os.path.exists(file_path) and os.path.isfile(file_path)
        except Exception:
            return False
    
    @staticmethod
    def sanitize_filename(filename):
        """تنظيف اسم الملف من الرموز الخاصة"""
        # إزالة الرموز الخاصة
        safe_name = re.sub(r'[<>:"/\\|?*]', '', filename)
        # استبدال المسافات بشرطات سفلية
        safe_name = re.sub(r'\s+', '_', safe_name.strip())
        # إزالة النقاط المتتالية
        safe_name = re.sub(r'\.+', '.', safe_name)
        # التأكد من عدم بدء أو انتهاء الاسم بنقطة
        safe_name = safe_name.strip('.')
        
        return safe_name if safe_name else 'unnamed_file'
    
    @staticmethod
    def copy_to_temp(file_path):
        """نسخ الملف إلى مجلد مؤقت بمسار آمن"""
        try:
            if not FileHelper.is_valid_path(file_path):
                return None
            
            # إنشاء مجلد مؤقت
            temp_dir = tempfile.mkdtemp()
            
            # الحصول على اسم الملف وتنظيفه
            original_name = os.path.basename(file_path)
            safe_name = FileHelper.sanitize_filename(original_name)
            
            # مسار الملف المؤقت
            temp_path = os.path.join(temp_dir, safe_name)
            
            # نسخ الملف
            shutil.copy2(file_path, temp_path)
            
            return temp_path
            
        except Exception as e:
            print(f"خطأ في نسخ الملف: {e}")
            return None
    
    @staticmethod
    def safe_read_image(file_path):
        """قراءة صورة بطريقة آمنة مع التعامل مع مسارات معقدة"""
        image = None
        
        # الطريقة الأولى: OpenCV مباشرة
        try:
            image = cv2.imread(file_path)
            if image is not None:
                return image
        except Exception:
            pass
        
        # الطريقة الثانية: قراءة البيانات الخام
        try:
            with open(file_path, 'rb') as f:
                file_bytes = np.frombuffer(f.read(), dtype=np.uint8)
                image = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)
                if image is not None:
                    return image
        except Exception:
            pass
        
        # الطريقة الثالثة: استخدام PIL
        try:
            pil_image = Image.open(file_path)
            
            # تحويل إلى RGB إذا لزم الأمر
            if pil_image.mode == 'RGBA':
                pil_image = pil_image.convert('RGB')
            elif pil_image.mode == 'P':
                pil_image = pil_image.convert('RGB')
            elif pil_image.mode == 'L':
                pil_image = pil_image.convert('RGB')
            
            # تحويل إلى numpy array
            image_array = np.array(pil_image)
            # تحويل من RGB إلى BGR لـ OpenCV
            image = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
            return image
            
        except Exception:
            pass
        
        # الطريقة الرابعة: نسخ إلى مجلد مؤقت
        try:
            temp_path = FileHelper.copy_to_temp(file_path)
            if temp_path:
                image = cv2.imread(temp_path)
                # تنظيف الملف المؤقت
                try:
                    os.remove(temp_path)
                    os.rmdir(os.path.dirname(temp_path))
                except:
                    pass
                return image
        except Exception:
            pass
        
        return None
    
    @staticmethod
    def safe_write_image(file_path, image):
        """حفظ صورة بطريقة آمنة"""
        try:
            # التأكد من وجود المجلد
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # الطريقة الأولى: OpenCV
            success = cv2.imwrite(file_path, image)
            if success:
                return True
        except Exception:
            pass
        
        try:
            # الطريقة الثانية: PIL
            # تحويل من BGR إلى RGB
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(image_rgb)
            pil_image.save(file_path)
            return True
        except Exception:
            pass
        
        try:
            # الطريقة الثالثة: حفظ في مجلد مؤقت ثم نسخ
            temp_dir = tempfile.mkdtemp()
            temp_name = f"temp_image_{os.getpid()}.jpg"
            temp_path = os.path.join(temp_dir, temp_name)
            
            success = cv2.imwrite(temp_path, image)
            if success:
                shutil.copy2(temp_path, file_path)
                # تنظيف
                os.remove(temp_path)
                os.rmdir(temp_dir)
                return True
        except Exception:
            pass
        
        return False
    
    @staticmethod
    def safe_read_video(file_path):
        """فتح فيديو بطريقة آمنة"""
        cap = None
        
        # الطريقة الأولى: OpenCV مباشرة
        try:
            cap = cv2.VideoCapture(file_path)
            if cap.isOpened():
                return cap
            else:
                cap.release()
        except Exception:
            pass
        
        # الطريقة الثانية: نسخ إلى مجلد مؤقت
        try:
            temp_path = FileHelper.copy_to_temp(file_path)
            if temp_path:
                cap = cv2.VideoCapture(temp_path)
                if cap.isOpened():
                    # حفظ مسار الملف المؤقت للتنظيف لاحقاً
                    cap.temp_path = temp_path
                    return cap
                else:
                    # تنظيف إذا فشل
                    try:
                        os.remove(temp_path)
                        os.rmdir(os.path.dirname(temp_path))
                    except:
                        pass
        except Exception:
            pass
        
        return None
    
    @staticmethod
    def cleanup_temp_video(cap):
        """تنظيف ملف الفيديو المؤقت"""
        try:
            if hasattr(cap, 'temp_path'):
                temp_path = cap.temp_path
                cap.release()
                os.remove(temp_path)
                os.rmdir(os.path.dirname(temp_path))
        except Exception:
            pass
    
    @staticmethod
    def get_safe_output_path(input_path, output_dir, suffix, style):
        """إنشاء مسار إخراج آمن"""
        try:
            # الحصول على اسم الملف الأساسي
            base_name = os.path.splitext(os.path.basename(input_path))[0]
            extension = os.path.splitext(input_path)[1]
            
            # تنظيف الاسم
            safe_base_name = FileHelper.sanitize_filename(base_name)
            
            # إنشاء اسم الملف الجديد
            new_name = f"{safe_base_name}_{suffix}_{style}{extension}"
            
            # المسار الكامل
            output_path = os.path.join(output_dir, new_name)
            
            # التأكد من عدم وجود الملف (إضافة رقم إذا لزم الأمر)
            counter = 1
            original_output_path = output_path
            while os.path.exists(output_path):
                name_part = os.path.splitext(original_output_path)[0]
                ext_part = os.path.splitext(original_output_path)[1]
                output_path = f"{name_part}_{counter}{ext_part}"
                counter += 1
            
            return output_path
            
        except Exception:
            # مسار احتياطي
            import time
            timestamp = int(time.time())
            return os.path.join(output_dir, f"cartoon_output_{timestamp}.jpg")
    
    @staticmethod
    def validate_file_for_processing(file_path):
        """التحقق من صحة الملف للمعالجة"""
        try:
            if not FileHelper.is_valid_path(file_path):
                return False, "الملف غير موجود"
            
            # التحقق من حجم الملف
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return False, "الملف فارغ"
            
            if file_size > 500 * 1024 * 1024:  # 500 MB
                return False, "الملف كبير جداً (أكثر من 500 MB)"
            
            # التحقق من صيغة الملف
            valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif',
                              '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
            
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in valid_extensions:
                return False, f"صيغة الملف غير مدعومة: {file_ext}"
            
            return True, "الملف صالح للمعالجة"
            
        except Exception as e:
            return False, f"خطأ في فحص الملف: {e}"
