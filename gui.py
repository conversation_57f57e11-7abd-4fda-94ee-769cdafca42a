#!/usr/bin/env python3
"""
واجهة رسومية لبرنامج تحويل الفيديو إلى كرتون
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import time
from PIL import Image, ImageTk
import cv2

from video_cartoonizer import VideoC<PERSON>oonizer
from image_cartoonizer import ImageC<PERSON>oonizer
from utils import validate_video_file, get_video_info, create_directories
from config import CARTOON_STYLES, DEFAULT_OUTPUT_DIR

class CartoonConverterGUI:
    """واجهة رسومية لتحويل الفيديو والصور إلى كرتون"""

    def __init__(self, root):
        self.root = root
        self.root.title("محول الفيديو إلى كرتون - Video to Cartoon Converter")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')

        # متغيرات التطبيق
        self.input_file = tk.StringVar()
        self.output_dir = tk.StringVar(value=DEFAULT_OUTPUT_DIR)
        self.selected_style = tk.StringVar(value='classic')
        self.processing = False
        self.current_cartoonizer = None

        # إنشاء المجلدات المطلوبة
        create_directories(DEFAULT_OUTPUT_DIR, 'input', 'temp')

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إنشاء النمط
        style = ttk.Style()
        style.theme_use('clam')

        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # العنوان
        title_label = ttk.Label(main_frame, text="محول الفيديو والصور إلى كرتون",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # قسم اختيار الملف
        self.setup_file_selection(main_frame, 1)

        # قسم الإعدادات
        self.setup_settings(main_frame, 2)

        # قسم المعاينة
        self.setup_preview(main_frame, 3)

        # قسم التحكم
        self.setup_controls(main_frame, 4)

        # قسم التقدم والسجل
        self.setup_progress_log(main_frame, 5)

    def setup_file_selection(self, parent, row):
        """إعداد قسم اختيار الملف"""
        # إطار اختيار الملف
        file_frame = ttk.LabelFrame(parent, text="اختيار الملف", padding="10")
        file_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)

        # مسار الملف
        ttk.Label(file_frame, text="الملف:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        file_entry = ttk.Entry(file_frame, textvariable=self.input_file, width=50)
        file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        browse_btn = ttk.Button(file_frame, text="تصفح", command=self.browse_file)
        browse_btn.grid(row=0, column=2)

        # مجلد الإخراج
        ttk.Label(file_frame, text="مجلد الإخراج:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))

        output_entry = ttk.Entry(file_frame, textvariable=self.output_dir, width=50)
        output_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))

        output_browse_btn = ttk.Button(file_frame, text="تصفح", command=self.browse_output_dir)
        output_browse_btn.grid(row=1, column=2, pady=(10, 0))

    def setup_settings(self, parent, row):
        """إعداد قسم الإعدادات"""
        # إطار الإعدادات
        settings_frame = ttk.LabelFrame(parent, text="إعدادات التحويل", padding="10")
        settings_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # نمط الكرتون
        ttk.Label(settings_frame, text="نمط الكرتون:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        style_combo = ttk.Combobox(settings_frame, textvariable=self.selected_style,
                                  values=list(CARTOON_STYLES.keys()), state="readonly", width=15)
        style_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        style_combo.bind('<<ComboboxSelected>>', self.on_style_changed)

        # وصف النمط
        self.style_description = ttk.Label(settings_frame, text=self.get_style_description('classic'))
        self.style_description.grid(row=0, column=2, sticky=tk.W)

        # خيارات إضافية للفيديو
        self.video_options_frame = ttk.Frame(settings_frame)
        self.video_options_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        # خيار تحويل مقطع محدد
        self.segment_var = tk.BooleanVar()
        segment_check = ttk.Checkbutton(self.video_options_frame, text="تحويل مقطع محدد",
                                       variable=self.segment_var, command=self.toggle_segment_options)
        segment_check.grid(row=0, column=0, sticky=tk.W)

        # إعدادات المقطع
        self.segment_frame = ttk.Frame(self.video_options_frame)
        self.segment_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Label(self.segment_frame, text="البداية (ثانية):").grid(row=0, column=0, sticky=tk.W, padx=(20, 5))
        self.start_time = tk.StringVar(value="0")
        start_entry = ttk.Entry(self.segment_frame, textvariable=self.start_time, width=10)
        start_entry.grid(row=0, column=1, padx=(0, 10))

        ttk.Label(self.segment_frame, text="المدة (ثانية):").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.duration = tk.StringVar(value="10")
        duration_entry = ttk.Entry(self.segment_frame, textvariable=self.duration, width=10)
        duration_entry.grid(row=0, column=3)

        # إخفاء إعدادات المقطع في البداية
        self.segment_frame.grid_remove()

    def setup_preview(self, parent, row):
        """إعداد قسم المعاينة"""
        # إطار المعاينة
        preview_frame = ttk.LabelFrame(parent, text="معاينة", padding="10")
        preview_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.columnconfigure(1, weight=1)

        # الصورة الأصلية
        original_frame = ttk.Frame(preview_frame)
        original_frame.grid(row=0, column=0, padx=(0, 5), sticky=(tk.W, tk.E))

        ttk.Label(original_frame, text="الأصلية").pack()
        self.original_canvas = tk.Canvas(original_frame, width=200, height=150, bg='white')
        self.original_canvas.pack()

        # الصورة المحولة
        cartoon_frame = ttk.Frame(preview_frame)
        cartoon_frame.grid(row=0, column=1, padx=(5, 0), sticky=(tk.W, tk.E))

        ttk.Label(cartoon_frame, text="كرتون").pack()
        self.cartoon_canvas = tk.Canvas(cartoon_frame, width=200, height=150, bg='white')
        self.cartoon_canvas.pack()

        # زر المعاينة
        preview_btn = ttk.Button(preview_frame, text="معاينة سريعة", command=self.quick_preview)
        preview_btn.grid(row=1, column=0, columnspan=2, pady=(10, 0))

    def setup_controls(self, parent, row):
        """إعداد قسم التحكم"""
        # إطار التحكم
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=row, column=0, columnspan=3, pady=(0, 10))

        # أزرار التحكم
        self.start_btn = ttk.Button(control_frame, text="بدء التحويل",
                                   command=self.start_conversion, style='Accent.TButton')
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_btn = ttk.Button(control_frame, text="إيقاف",
                                  command=self.stop_conversion, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.open_output_btn = ttk.Button(control_frame, text="فتح مجلد النتائج",
                                         command=self.open_output_folder)
        self.open_output_btn.pack(side=tk.LEFT)

    def setup_progress_log(self, parent, row):
        """إعداد قسم التقدم والسجل"""
        # إطار التقدم
        progress_frame = ttk.LabelFrame(parent, text="التقدم", padding="10")
        progress_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        progress_frame.rowconfigure(1, weight=1)

        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                           maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # نص التقدم
        self.progress_text = tk.StringVar(value="جاهز للبدء")
        progress_label = ttk.Label(progress_frame, textvariable=self.progress_text)
        progress_label.grid(row=0, column=1, padx=(10, 0))

        # سجل العمليات
        self.log_text = scrolledtext.ScrolledText(progress_frame, height=8, width=80)
        self.log_text.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))

        # تكوين الشبكة للتمدد
        parent.rowconfigure(row, weight=1)

    def browse_file(self):
        """تصفح واختيار ملف الإدخال"""
        filetypes = [
            ("جميع الملفات المدعومة", "*.mp4;*.avi;*.mov;*.mkv;*.wmv;*.jpg;*.jpeg;*.png;*.bmp"),
            ("ملفات الفيديو", "*.mp4;*.avi;*.mov;*.mkv;*.wmv;*.flv;*.webm"),
            ("ملفات الصور", "*.jpg;*.jpeg;*.png;*.bmp;*.tiff"),
            ("جميع الملفات", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="اختر ملف الفيديو أو الصورة",
            filetypes=filetypes
        )

        if filename:
            self.input_file.set(filename)
            self.log_message(f"تم اختيار الملف: {filename}")

            # تحديث خيارات الفيديو حسب نوع الملف
            self.update_video_options(filename)

            # محاولة تحميل معاينة
            self.load_preview_image(filename)

    def browse_output_dir(self):
        """تصفح واختيار مجلد الإخراج"""
        directory = filedialog.askdirectory(
            title="اختر مجلد الإخراج",
            initialdir=self.output_dir.get()
        )

        if directory:
            self.output_dir.set(directory)
            self.log_message(f"تم تحديد مجلد الإخراج: {directory}")

    def update_video_options(self, filename):
        """تحديث خيارات الفيديو حسب نوع الملف"""
        if self.is_video_file(filename):
            # إظهار خيارات الفيديو
            self.video_options_frame.grid()
            try:
                # الحصول على معلومات الفيديو
                video_info = get_video_info(filename)
                duration = video_info.get('duration', 0)
                self.log_message(f"مدة الفيديو: {duration:.2f} ثانية")

                # تحديث القيم الافتراضية
                self.duration.set(str(min(10, int(duration))))

            except Exception as e:
                self.log_message(f"خطأ في قراءة معلومات الفيديو: {e}")
        else:
            # إخفاء خيارات الفيديو للصور
            self.video_options_frame.grid_remove()

    def is_video_file(self, filename):
        """التحقق من كون الملف فيديو"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        return any(filename.lower().endswith(ext) for ext in video_extensions)

    def on_style_changed(self, event=None):
        """عند تغيير نمط الكرتون"""
        style = self.selected_style.get()
        description = self.get_style_description(style)
        self.style_description.config(text=description)
        self.log_message(f"تم تغيير النمط إلى: {style}")

    def get_style_description(self, style):
        """الحصول على وصف النمط"""
        descriptions = {
            'classic': "متوازن - مناسب لمعظم الاستخدامات",
            'smooth': "ناعم - مناسب للمناظر الطبيعية",
            'sharp': "حاد - مناسب للشخصيات والرسوم"
        }
        return descriptions.get(style, "")

    def toggle_segment_options(self):
        """تبديل إظهار/إخفاء خيارات المقطع"""
        if self.segment_var.get():
            self.segment_frame.grid()
        else:
            self.segment_frame.grid_remove()

    def load_preview_image(self, filename):
        """تحميل صورة المعاينة"""
        try:
            if self.is_video_file(filename):
                # استخراج إطار من الفيديو
                cap = cv2.VideoCapture(filename)
                ret, frame = cap.read()
                cap.release()

                if ret:
                    # تحويل من BGR إلى RGB
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    self.display_image(frame, self.original_canvas)
                else:
                    self.log_message("لا يمكن استخراج إطار من الفيديو")
            else:
                # تحميل الصورة
                image = cv2.imread(filename)
                if image is not None:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    self.display_image(image, self.original_canvas)
                else:
                    self.log_message("لا يمكن تحميل الصورة")

        except Exception as e:
            self.log_message(f"خطأ في تحميل المعاينة: {e}")

    def display_image(self, image, canvas):
        """عرض صورة في Canvas"""
        try:
            # تغيير حجم الصورة لتناسب Canvas
            height, width = image.shape[:2]
            canvas_width = canvas.winfo_width() or 200
            canvas_height = canvas.winfo_height() or 150

            # حساب النسبة
            scale = min(canvas_width/width, canvas_height/height)
            new_width = int(width * scale)
            new_height = int(height * scale)

            # تغيير الحجم
            pil_image = Image.fromarray(image)
            pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            # تحويل إلى PhotoImage
            photo = ImageTk.PhotoImage(pil_image)

            # عرض في Canvas
            canvas.delete("all")
            canvas.create_image(canvas_width//2, canvas_height//2, image=photo)

            # حفظ مرجع للصورة لمنع garbage collection
            canvas.image = photo

        except Exception as e:
            self.log_message(f"خطأ في عرض الصورة: {e}")

    def quick_preview(self):
        """معاينة سريعة للتحويل"""
        if not self.input_file.get():
            messagebox.showwarning("تحذير", "يرجى اختيار ملف أولاً")
            return

        try:
            self.log_message("بدء المعاينة السريعة...")

            # إنشاء محول الصور
            cartoonizer = ImageCartoonizer(self.selected_style.get())

            if self.is_video_file(self.input_file.get()):
                # استخراج إطار من الفيديو
                cap = cv2.VideoCapture(self.input_file.get())
                ret, frame = cap.read()
                cap.release()

                if not ret:
                    messagebox.showerror("خطأ", "لا يمكن قراءة الفيديو")
                    return

                original_image = frame
            else:
                # تحميل الصورة
                original_image = cv2.imread(self.input_file.get())
                if original_image is None:
                    messagebox.showerror("خطأ", "لا يمكن قراءة الصورة")
                    return

            # تحويل إلى كرتون
            cartoon_image = cartoonizer.cartoonize_image(original_image)

            # عرض النتائج
            original_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
            cartoon_rgb = cv2.cvtColor(cartoon_image, cv2.COLOR_BGR2RGB)

            self.display_image(original_rgb, self.original_canvas)
            self.display_image(cartoon_rgb, self.cartoon_canvas)

            self.log_message("تمت المعاينة السريعة بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في المعاينة: {e}")
            self.log_message(f"خطأ في المعاينة: {e}")

    def log_message(self, message):
        """إضافة رسالة إلى السجل"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def start_conversion(self):
        """بدء عملية التحويل"""
        if not self.input_file.get():
            messagebox.showwarning("تحذير", "يرجى اختيار ملف أولاً")
            return

        if not os.path.exists(self.input_file.get()):
            messagebox.showerror("خطأ", "الملف المحدد غير موجود")
            return

        # التحقق من مجلد الإخراج
        output_dir = self.output_dir.get()
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                self.log_message(f"تم إنشاء مجلد الإخراج: {output_dir}")
            except Exception as e:
                messagebox.showerror("خطأ", f"لا يمكن إنشاء مجلد الإخراج: {e}")
                return

        # تعطيل أزرار التحكم
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.processing = True

        # إعادة تعيين شريط التقدم
        self.progress_var.set(0)
        self.progress_text.set("بدء المعالجة...")

        # بدء المعالجة في thread منفصل
        self.conversion_thread = threading.Thread(target=self.run_conversion)
        self.conversion_thread.daemon = True
        self.conversion_thread.start()

    def run_conversion(self):
        """تشغيل عملية التحويل"""
        try:
            input_file = self.input_file.get()
            style = self.selected_style.get()
            output_dir = self.output_dir.get()

            if self.is_video_file(input_file):
                self.convert_video(input_file, style, output_dir)
            else:
                self.convert_image(input_file, style, output_dir)

        except Exception as e:
            self.log_message(f"خطأ في التحويل: {e}")
            messagebox.showerror("خطأ", f"خطأ في التحويل: {e}")
        finally:
            # إعادة تفعيل الأزرار
            self.root.after(0, self.conversion_finished)

    def convert_image(self, input_file, style, output_dir):
        """تحويل صورة واحدة"""
        self.log_message(f"بدء تحويل الصورة: {os.path.basename(input_file)}")

        # إنشاء محول الصور
        cartoonizer = ImageCartoonizer(style)

        # قراءة الصورة
        image = cv2.imread(input_file)
        if image is None:
            raise ValueError("لا يمكن قراءة الصورة")

        # تحديث التقدم
        self.root.after(0, lambda: self.progress_var.set(25))
        self.root.after(0, lambda: self.progress_text.set("معالجة الصورة..."))

        # تحويل إلى كرتون
        cartoon_image = cartoonizer.cartoonize_image(image)

        # تحديث التقدم
        self.root.after(0, lambda: self.progress_var.set(75))
        self.root.after(0, lambda: self.progress_text.set("حفظ النتيجة..."))

        # تحديد مسار الإخراج
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        extension = os.path.splitext(input_file)[1]
        output_path = os.path.join(output_dir, f"{base_name}_cartoon_{style}{extension}")

        # حفظ الصورة
        cv2.imwrite(output_path, cartoon_image)

        # تحديث التقدم
        self.root.after(0, lambda: self.progress_var.set(100))
        self.root.after(0, lambda: self.progress_text.set("تم الانتهاء"))

        self.log_message(f"تم حفظ الصورة في: {output_path}")

        # عرض النتيجة في المعاينة
        cartoon_rgb = cv2.cvtColor(cartoon_image, cv2.COLOR_BGR2RGB)
        self.root.after(0, lambda: self.display_image(cartoon_rgb, self.cartoon_canvas))

    def convert_video(self, input_file, style, output_dir):
        """تحويل فيديو"""
        self.log_message(f"بدء تحويل الفيديو: {os.path.basename(input_file)}")

        # إنشاء محول الفيديو
        cartoonizer = VideoCartoonizer(style, output_dir)

        # دالة callback لتحديث التقدم
        def progress_callback(current_frame, total_frames):
            if not self.processing:
                return False  # إيقاف المعالجة

            progress = (current_frame / total_frames) * 100
            self.root.after(0, lambda: self.progress_var.set(progress))
            self.root.after(0, lambda: self.progress_text.set(f"معالجة الإطار {current_frame}/{total_frames}"))
            return True

        try:
            if self.segment_var.get():
                # تحويل مقطع محدد
                start_time = float(self.start_time.get())
                duration = float(self.duration.get())

                self.log_message(f"تحويل مقطع من {start_time}ث لمدة {duration}ث")
                output_path = cartoonizer.cartoonize_video_segment(
                    input_file, start_time, duration, None
                )
            else:
                # تحويل الفيديو كاملاً
                output_path = cartoonizer.cartoonize_video(
                    input_file, None, progress_callback
                )

            self.log_message(f"تم حفظ الفيديو في: {output_path}")

        except ValueError as e:
            if "البداية" in str(e) or "المدة" in str(e):
                raise ValueError("يرجى إدخال قيم صحيحة لوقت البداية والمدة")
            raise

    def stop_conversion(self):
        """إيقاف عملية التحويل"""
        self.processing = False
        self.log_message("تم طلب إيقاف المعالجة...")
        self.progress_text.set("جاري الإيقاف...")

    def conversion_finished(self):
        """عند انتهاء عملية التحويل"""
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.processing = False

        if self.progress_var.get() >= 100:
            self.progress_text.set("تم الانتهاء بنجاح")
            messagebox.showinfo("نجح", "تم التحويل بنجاح!")
        else:
            self.progress_text.set("تم الإيقاف")

    def open_output_folder(self):
        """فتح مجلد النتائج"""
        output_dir = self.output_dir.get()
        if os.path.exists(output_dir):
            try:
                # فتح المجلد في مستكشف الملفات
                if os.name == 'nt':  # Windows
                    os.startfile(output_dir)
                elif os.name == 'posix':  # macOS and Linux
                    os.system(f'open "{output_dir}"' if os.uname().sysname == 'Darwin' else f'xdg-open "{output_dir}"')

                self.log_message(f"تم فتح مجلد النتائج: {output_dir}")
            except Exception as e:
                self.log_message(f"خطأ في فتح المجلد: {e}")
                messagebox.showerror("خطأ", f"لا يمكن فتح المجلد: {e}")
        else:
            messagebox.showwarning("تحذير", "مجلد النتائج غير موجود")


def main():
    """الدالة الرئيسية لتشغيل الواجهة الرسومية"""
    root = tk.Tk()
    app = CartoonConverterGUI(root)

    # إعداد إغلاق التطبيق
    def on_closing():
        if app.processing:
            if messagebox.askokcancel("إغلاق", "هناك عملية معالجة جارية. هل تريد الإغلاق؟"):
                app.processing = False
                root.destroy()
        else:
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # تشغيل التطبيق
    root.mainloop()


if __name__ == "__main__":
    main()
