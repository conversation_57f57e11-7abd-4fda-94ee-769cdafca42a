# دليل البدء السريع - برنامج تحويل الفيديو إلى كرتون

## التثبيت السريع

```bash
# تثبيت المتطلبات
pip install opencv-python numpy matplotlib Pillow tqdm

# تشغيل العرض التجريبي
python demo.py
```

## الاستخدام السريع

### 1. تحويل صورة واحدة
```bash
python main.py --image photo.jpg
```

### 2. تحويل فيديو كامل
```bash
python main.py video.mp4
```

### 3. تحويل مقطع من فيديو (من الثانية 10 لمدة 30 ثانية)
```bash
python main.py video.mp4 --segment 10 30
```

### 4. اختيار نمط الكرتون
```bash
python main.py video.mp4 --style smooth
```

## الأنماط المتاحة

- **classic**: النمط الافتراضي - توازن جيد
- **smooth**: نمط ناعم - للمشاهد الطبيعية
- **sharp**: نمط حاد - للرسوم المتحركة

## أمثلة متقدمة

### تحويل جميع الفيديوهات في مجلد
```bash
for file in input/*.mp4; do
    python main.py "$file" --style classic
done
```

### تحويل بإعدادات مخصصة
```bash
python main.py video.mp4 --style sharp --output my_cartoon.mp4
```

## نصائح للحصول على أفضل النتائج

1. **جودة الفيديو**: استخدم فيديوهات بدقة 1080p أو أقل
2. **الإضاءة**: الفيديوهات ذات الإضاءة الجيدة تعطي نتائج أفضل
3. **المحتوى**: الوجوه والمناظر الطبيعية تعمل بشكل ممتاز
4. **الحجم**: للفيديوهات الكبيرة، استخدم خيار `--segment`

## استكشاف الأخطاء

### مشكلة: "لا يمكن فتح الفيديو"
```bash
# تحقق من صيغة الفيديو
python main.py video.mp4 --info
```

### مشكلة: بطء في المعالجة
```bash
# استخدم النمط الكلاسيكي للسرعة
python main.py video.mp4 --style classic
```

### مشكلة: نفاد الذاكرة
```bash
# قم بمعالجة مقاطع أصغر
python main.py video.mp4 --segment 0 30
```

## الملفات المهمة

- `main.py` - البرنامج الرئيسي
- `demo.py` - العرض التجريبي
- `input/` - ضع ملفاتك هنا
- `output/` - النتائج تُحفظ هنا

## الدعم

للمساعدة أو الإبلاغ عن مشاكل، راجع ملف README.md للتفاصيل الكاملة.
