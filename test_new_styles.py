#!/usr/bin/env python3
"""
اختبار الأنماط الجديدة لتحويل الصور إلى كرتون
"""

import cv2
import numpy as np
import os
import time
from image_cartoonizer import ImageCartoonizer
from config import CARTOON_STYLES, STYLE_DESCRIPTIONS

def create_test_image():
    """إنشاء صورة اختبار متنوعة"""
    # إنشاء صورة بحجم مناسب
    height, width = 400, 600
    image = np.zeros((height, width, 3), dtype=np.uint8)

    # خلفية متدرجة
    for i in range(height):
        for j in range(width):
            r = int(np.clip(200 + 55 * np.sin(i * 0.02), 0, 255))
            g = int(np.clip(150 + 105 * np.cos(j * 0.015), 0, 255))
            b = int(np.clip(100 + 155 * np.sin((i + j) * 0.01), 0, 255))
            image[i, j] = [b, g, r]  # BGR format for OpenCV

    # إضافة أشكال مختلفة لاختبار التأثيرات

    # دائرة كبيرة (وجه)
    cv2.circle(image, (200, 150), 80, (220, 180, 140), -1)  # لون بشرة

    # عيون
    cv2.circle(image, (180, 130), 15, (50, 50, 50), -1)
    cv2.circle(image, (220, 130), 15, (50, 50, 50), -1)
    cv2.circle(image, (185, 125), 5, (255, 255, 255), -1)
    cv2.circle(image, (225, 125), 5, (255, 255, 255), -1)

    # فم
    cv2.ellipse(image, (200, 170), (20, 10), 0, 0, 180, (200, 100, 100), -1)

    # مستطيلات ملونة
    cv2.rectangle(image, (50, 250), (150, 350), (255, 100, 100), -1)  # أحمر
    cv2.rectangle(image, (200, 250), (300, 350), (100, 255, 100), -1)  # أخضر
    cv2.rectangle(image, (350, 250), (450, 350), (100, 100, 255), -1)  # أزرق

    # مثلثات
    points1 = np.array([[500, 100], [450, 200], [550, 200]], np.int32)
    cv2.fillPoly(image, [points1], (255, 255, 100))  # أصفر

    points2 = np.array([[100, 50], [50, 150], [150, 150]], np.int32)
    cv2.fillPoly(image, [points2], (255, 100, 255))  # بنفسجي

    # خطوط وتفاصيل
    cv2.line(image, (0, 200), (600, 200), (255, 255, 255), 3)
    cv2.line(image, (300, 0), (300, 400), (255, 255, 255), 2)

    # نص
    cv2.putText(image, 'TEST', (400, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 3)
    cv2.putText(image, 'STYLES', (400, 380), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)

    return image

def test_all_styles():
    """اختبار جميع الأنماط الجديدة"""
    print("🎨 اختبار الأنماط الجديدة لتحويل الصور إلى كرتون")
    print("=" * 60)

    # إنشاء المجلدات
    os.makedirs('input', exist_ok=True)
    os.makedirs('output', exist_ok=True)

    # إنشاء صورة الاختبار
    test_image = create_test_image()
    input_path = 'input/style_test_image.jpg'
    cv2.imwrite(input_path, test_image)
    print(f"✓ تم إنشاء صورة الاختبار: {input_path}")

    # اختبار كل نمط
    results = {}

    for style_name in CARTOON_STYLES.keys():
        print(f"\n🎭 اختبار النمط: {style_name}")
        print(f"📝 الوصف: {STYLE_DESCRIPTIONS.get(style_name, 'غير متوفر')}")

        try:
            # إنشاء محول الصور
            cartoonizer = ImageCartoonizer(style_name)

            # قياس الوقت
            start_time = time.time()

            # تحويل الصورة
            cartoon_image = cartoonizer.cartoonize_image(test_image)

            # حساب الوقت المستغرق
            processing_time = time.time() - start_time

            # حفظ النتيجة
            output_path = f'output/style_test_{style_name}.jpg'
            cv2.imwrite(output_path, cartoon_image)

            # تسجيل النتائج
            results[style_name] = {
                'success': True,
                'time': processing_time,
                'output_path': output_path
            }

            print(f"✅ نجح التحويل في {processing_time:.2f} ثانية")
            print(f"📁 حُفظ في: {output_path}")

        except Exception as e:
            results[style_name] = {
                'success': False,
                'error': str(e),
                'time': 0
            }
            print(f"❌ فشل التحويل: {e}")

    return results

def analyze_results(results):
    """تحليل نتائج الاختبار"""
    print("\n" + "=" * 60)
    print("📊 تحليل النتائج")
    print("=" * 60)

    successful_styles = [name for name, result in results.items() if result['success']]
    failed_styles = [name for name, result in results.items() if not result['success']]

    print(f"✅ الأنماط الناجحة: {len(successful_styles)}/{len(results)}")
    print(f"❌ الأنماط الفاشلة: {len(failed_styles)}")

    if successful_styles:
        print(f"\n🎯 الأنماط الناجحة:")
        for style in successful_styles:
            time_taken = results[style]['time']
            print(f"  • {style}: {time_taken:.2f}ث - {STYLE_DESCRIPTIONS.get(style, '')}")

        # أسرع وأبطأ نمط
        times = [(name, result['time']) for name, result in results.items() if result['success']]
        if times:
            fastest = min(times, key=lambda x: x[1])
            slowest = max(times, key=lambda x: x[1])

            print(f"\n⚡ أسرع نمط: {fastest[0]} ({fastest[1]:.2f}ث)")
            print(f"🐌 أبطأ نمط: {slowest[0]} ({slowest[1]:.2f}ث)")

    if failed_styles:
        print(f"\n❌ الأنماط الفاشلة:")
        for style in failed_styles:
            error = results[style]['error']
            print(f"  • {style}: {error}")

def compare_styles():
    """مقارنة الأنماط المختلفة"""
    print("\n" + "=" * 60)
    print("🔍 مقارنة الأنماط")
    print("=" * 60)

    # تصنيف الأنماط
    categories = {
        'أساسية': ['classic', 'smooth', 'sharp'],
        'أفلام كرتون': ['anime', 'disney', 'pixar'],
        'فنية': ['watercolor', 'oil_painting', 'comic'],
        'خاصة': ['vintage_cartoon', 'neon']
    }

    for category, styles in categories.items():
        print(f"\n📂 {category}:")
        for style in styles:
            if style in CARTOON_STYLES:
                desc = STYLE_DESCRIPTIONS.get(style, '')
                params = CARTOON_STYLES[style]
                colors = params.get('num_colors', 'غير محدد')
                saturation = params.get('color_saturation', 1.0)

                print(f"  🎨 {style}:")
                print(f"     📝 {desc}")
                print(f"     🎯 عدد الألوان: {colors}")
                print(f"     💫 تشبع الألوان: {saturation}x")

                # التأثيرات الخاصة
                special_effects = []
                for key, value in params.items():
                    if key.startswith('use_') and value:
                        effect_name = key.replace('use_', '').replace('_', ' ')
                        special_effects.append(effect_name)

                if special_effects:
                    print(f"     ✨ تأثيرات خاصة: {', '.join(special_effects)}")

def create_style_comparison():
    """إنشاء مقارنة بصرية للأنماط"""
    print(f"\n🖼️ إنشاء مقارنة بصرية...")

    # قراءة الصورة الأصلية
    original = cv2.imread('input/style_test_image.jpg')
    if original is None:
        print("❌ لا يمكن قراءة الصورة الأصلية")
        return

    # تصغير الصور للمقارنة
    small_size = (200, 150)
    original_small = cv2.resize(original, small_size)

    # إنشاء لوحة المقارنة
    styles_to_compare = ['classic', 'anime', 'disney', 'comic', 'neon', 'watercolor']

    # حساب أبعاد اللوحة
    cols = 3
    rows = (len(styles_to_compare) + 1 + cols - 1) // cols  # +1 للصورة الأصلية

    comparison_width = cols * small_size[0]
    comparison_height = rows * small_size[1]
    comparison = np.zeros((comparison_height, comparison_width, 3), dtype=np.uint8)

    # إضافة الصورة الأصلية
    comparison[0:small_size[1], 0:small_size[0]] = original_small
    cv2.putText(comparison, 'Original', (5, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

    # إضافة الأنماط
    for i, style in enumerate(styles_to_compare):
        row = (i + 1) // cols
        col = (i + 1) % cols

        y_start = row * small_size[1]
        y_end = y_start + small_size[1]
        x_start = col * small_size[0]
        x_end = x_start + small_size[0]

        style_path = f'output/style_test_{style}.jpg'
        if os.path.exists(style_path):
            style_img = cv2.imread(style_path)
            style_small = cv2.resize(style_img, small_size)
            comparison[y_start:y_end, x_start:x_end] = style_small
            cv2.putText(comparison, style, (x_start + 5, y_start + 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

    # حفظ المقارنة
    comparison_path = 'output/styles_comparison.jpg'
    cv2.imwrite(comparison_path, comparison)
    print(f"✅ تم حفظ المقارنة في: {comparison_path}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار الأنماط الجديدة...")

    try:
        # اختبار جميع الأنماط
        results = test_all_styles()

        # تحليل النتائج
        analyze_results(results)

        # مقارنة الأنماط
        compare_styles()

        # إنشاء مقارنة بصرية
        create_style_comparison()

        print(f"\n🎉 انتهى الاختبار بنجاح!")
        print(f"📁 تحقق من مجلد 'output' لرؤية النتائج")

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
